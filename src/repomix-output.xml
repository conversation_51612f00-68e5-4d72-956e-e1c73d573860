This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
ability/
  ability_component.gd
  ability_component.gd.uid
  ability_component.tscn
  ability_data.gd
  ability_data.gd.uid
  ability_system.gd.uid
  ability.gd
  ability.gd.uid
  inverted_controls_ability_data.gd
  inverted_controls_ability_data.gd.uid
  inverted_controls_ability.gd
  inverted_controls_ability.gd.uid
  inverted_controls_system.gd
  inverted_controls_system.gd.uid
  inverted_controls_system.tscn
  maintain_normal_time_ability_data.gd
  maintain_normal_time_ability_data.gd.uid
  multi_drop_ability_data.gd
  multi_drop_ability_data.gd.uid
  restore_paint_ability_data.gd
  restore_paint_ability_data.gd.uid
  wall_phase_ability_data.gd
  wall_phase_ability_data.gd.uid
  wall_phase_system.gd
  wall_phase_system.gd.uid
  wall_phase_system.tscn
cog/
  cog_bronze.tscn
  cog_silver.tscn
  cog.gd
  cog.gd.uid
color_tile/
  color_tile.gd
  color_tile.gd.uid
  color_tile.tscn
damage/
  damage_component.gd
  damage_component.gd.uid
  damage_component.tscn
  damage_data.gd
  damage_data.gd.uid
  damage_system.gd
  damage_system.gd.uid
  damage_system.tscn
dev_tools/
  dev_tools.gd
  dev_tools.gd.uid
  dev_tools.tscn
eraser/
  eraser.gd
  eraser.gd.uid
  eraser.tscn
game_progress/
  game_progress.gd
  game_progress.gd.uid
game_state/
  game_state_service.gd
  game_state_service.gd.uid
health/
  health_component.gd
  health_component.gd.uid
  health_component.tscn
  health_data.gd
  health_data.gd.uid
  health_system.gd
  health_system.gd.uid
  health_system.tscn
hub/
  hub.gd
  hub.gd.uid
  hub.tscn
  portal.gd
  portal.gd.uid
  portal.tscn
level/
  conditions/
    all_players_dead_condition_data.gd
    all_players_dead_condition_data.gd.uid
    all_players_dead_condition_system.gd
    all_players_dead_condition_system.gd.uid
    condition_data.gd
    condition_data.gd.uid
    condition_system.gd
    condition_system.gd.uid
    loss_by_death.tres
    paint_all_tiles_condition_data.gd
    paint_all_tiles_condition_data.gd.uid
    paint_all_tiles_condition_system.gd
    paint_all_tiles_condition_system.gd.uid
    win_by_painting.tres
  outcome_system/
    level_outcome_system.gd
    level_outcome_system.gd.uid
    level_outcome_system.tscn
  progress_system/
    level_event_data.gd
    level_event_data.gd.uid
    level_progress_system.gd
    level_progress_system.gd.uid
    level_progress_system.tscn
  base_level.gd
  base_level.gd.uid
  level_state_component.gd
  level_state_component.gd.uid
  level_state_component.tscn
  level_state_data.gd
  level_state_data.gd.uid
  level.gd
  level.gd.uid
  level.tscn
levels/
  level_1/
    level_1_1.tscn
    level_1_2.tscn
    level_1_3.tscn
    level_1_4.tscn
loss_ui/
  loss_ui.gd
  loss_ui.gd.uid
  loss_ui.tscn
main/
  game_orchestrator.gd
  game_orchestrator.gd.uid
  main.gd
  main.gd.uid
  main.tscn
movement/
  movement_component.gd
  movement_component.gd.uid
  movement_component.tscn
  movement_data.gd
  movement_data.gd.uid
  movement_system.gd
  movement_system.gd.uid
  movement_system.tscn
paint/
  paint_component.gd
  paint_component.gd.uid
  paint_component.tscn
  paint_data.gd
  paint_data.gd.uid
  paint_system.gd
  paint_system.gd.uid
  paint_system.tscn
paint_bar/
  paint_bar.gd
  paint_bar.gd.uid
  paint_bar.tscn
  paint_drop.tscn
paint_refill/
  paint_refill_component.gd
  paint_refill_component.gd.uid
  paint_refill_component.tscn
  paint_refill_data.gd
  paint_refill_data.gd.uid
  paint_refill_drop.tscn
  paint_refill_system.gd.uid
player/
  characters/
    apple.tscn
    banana.tscn
    kiwi.tscn
    orange.tscn
  input_component.gd
  input_component.gd.uid
  input_component.tscn
  input_data.gd
  input_data.gd.uid
  player_base_stats_data.gd
  player_base_stats_data.gd.uid
  player_input_system.gd
  player_input_system.gd.uid
  player_input_system.tscn
  player_service.gd
  player_service.gd.uid
  player.gd
  player.gd.uid
  player.tscn
  stats_component.gd
  stats_component.gd.uid
  stats_component.tscn
progress_bar/
  event_marker.gd
  event_marker.gd.uid
  event_marker.tscn
  progress_bar.gd
  progress_bar.gd.uid
  progress_bar.tscn
run_state/
  run_state_service.gd
  run_state_service.gd.uid
stats/
  stat_service.gd
  stat_service.gd.uid
tile_query_system/
  tile_query_system.gd
  tile_query_system.gd.uid
  tile_query_system.tscn
time_dilation/
  time_dilation_system.gd
  time_dilation_system.gd.uid
  time_dilation_system.tscn
  time_sensitive_component.gd
  time_sensitive_component.gd.uid
  time_sensitive_component.tscn
  time_sensitive_data.gd
  time_sensitive_data.gd.uid
water/
  water_system.gd
  water_system.gd.uid
  water_system.tscn
water_tile/
  water_tile_component.gd
  water_tile_component.gd.uid
  water_tile_data.gd
  water_tile_data.gd.uid
  water_tile.gd.uid
  water_tile.tscn
win_ui/
  win_ui.gd
  win_ui.gd.uid
  win_ui.tscn
README.md
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="ability/ability_component.gd">
class_name AbilityComponent
extends Node

@export var ability_data_list: Array[AbilityData]

func _ready() -> void:
	_initialize_abilities()

func _initialize_abilities() -> void:
	for ability_data: AbilityData in ability_data_list:
		_add_ability(ability_data)

func _add_ability(ability_data: AbilityData) -> void:
	var ability_node: Ability = null

	if ability_data is InvertedControlsAbilityData:
		ability_node = InvertedControlsAbility.new()
	else:
		ability_node = Ability.new()

	ability_node.data = ability_data
	add_child(ability_node)

func get_ability(ability_type: GDScript) -> Ability:
	for child: Node in get_children():
		if child is Ability:
			var ability: Ability = child as Ability
			if ability.data != null and ability.data.get_script() == ability_type:
				return ability
	return null

func has_ability(ability_type: GDScript) -> bool:
	return get_ability(ability_type) != null

func remove_ability(ability: Ability) -> void:
	if ability != null and ability.get_parent() == self:
		ability.queue_free()
</file>

<file path="ability/ability_component.gd.uid">
uid://clp1oj2ktpjdh
</file>

<file path="ability/ability_component.tscn">
[gd_scene load_steps=2 format=3 uid="uid://d0ntxy5trg0re"]

[ext_resource type="Script" uid="uid://clp1oj2ktpjdh" path="res://src/ability/ability_component.gd" id="1_component"]

[node name="AbilityComponent" type="Node"]
script = ExtResource("1_component")
</file>

<file path="ability/ability_data.gd">
class_name AbilityData
extends Resource
</file>

<file path="ability/ability_data.gd.uid">
uid://72hm77eoq5jd
</file>

<file path="ability/ability_system.gd.uid">
uid://cai4ab83tuwj2
</file>

<file path="ability/ability.gd">
class_name Ability
extends Node

@export var data: AbilityData
</file>

<file path="ability/ability.gd.uid">
uid://bh4duqotw5ntl
</file>

<file path="ability/inverted_controls_ability_data.gd">
class_name InvertedControlsAbilityData
extends AbilityData

@export var duration: float = 10.0
@export var time_scale_on_move: float = 3.0
</file>

<file path="ability/inverted_controls_ability_data.gd.uid">
uid://48vafofprcn5
</file>

<file path="ability/inverted_controls_ability.gd">
class_name InvertedControlsAbility
extends Ability

var remaining_time: float = 0.0

func _ready() -> void:
	var ability_data := data as InvertedControlsAbilityData
	remaining_time = ability_data.duration

func initialize() -> void:
	var ability_data := data as InvertedControlsAbilityData
	remaining_time = ability_data.duration
</file>

<file path="ability/inverted_controls_ability.gd.uid">
uid://cnmm2wx4nwtqq
</file>

<file path="ability/inverted_controls_system.gd">
class_name InvertedControlsSystem
extends Node

@export var player_service: PlayerService

var _player_ability_component: AbilityComponent
var _player_movement_component: MovementComponent
var _is_player_moving: bool = false

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	_player_ability_component = player_service.get_ability_component()
	_player_movement_component = player_service.get_movement_component()

	if is_instance_valid(_player_movement_component):
		_player_movement_component.movement_started.connect(_on_player_movement_started)
		_player_movement_component.movement_completed.connect(_on_player_movement_completed)

func _process(delta: float) -> void:
	if not is_instance_valid(_player_ability_component):
		return

	var inverted_ability: InvertedControlsAbility = _player_ability_component.get_ability(InvertedControlsAbilityData) as InvertedControlsAbility
	if not is_instance_valid(inverted_ability):
		return

	var ability_data: InvertedControlsAbilityData = inverted_ability.data as InvertedControlsAbilityData
	if inverted_ability.remaining_time == 0.0 and ability_data.duration > 0.0:
		inverted_ability.initialize()

	var effective_delta: float = delta
	if _is_player_moving:
		effective_delta *= ability_data.time_scale_on_move

	inverted_ability.remaining_time -= effective_delta

	if inverted_ability.remaining_time <= 0.0:
		_player_ability_component.remove_ability(inverted_ability)

func _on_player_movement_started(_direction: Vector2) -> void:
	_is_player_moving = true

func _on_player_movement_completed() -> void:
	_is_player_moving = false
</file>

<file path="ability/inverted_controls_system.gd.uid">
uid://coli2u2064v0u
</file>

<file path="ability/inverted_controls_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://cne4tjc17alt0"]

[ext_resource type="Script" uid="uid://coli2u2064v0u" path="res://src/ability/inverted_controls_system.gd" id="1_system"]

[node name="InvertedControlsSystem" type="Node"]
script = ExtResource("1_system")
</file>

<file path="ability/maintain_normal_time_ability_data.gd">
class_name MaintainNormalTimeAbilityData
extends AbilityData
</file>

<file path="ability/maintain_normal_time_ability_data.gd.uid">
uid://5g3i8wjai3b2
</file>

<file path="ability/multi_drop_ability_data.gd">
class_name MultiDropAbilityData
extends AbilityData

@export var drop_count: int = 3
</file>

<file path="ability/multi_drop_ability_data.gd.uid">
uid://bkusl4o508on1
</file>

<file path="ability/restore_paint_ability_data.gd">
class_name RestorePaintAbilityData
extends AbilityData

@export var paint_to_restore: int = 1
</file>

<file path="ability/restore_paint_ability_data.gd.uid">
uid://dcms2verply6s
</file>

<file path="ability/wall_phase_ability_data.gd">
class_name WallPhaseAbilityData
extends AbilityData

@export var paint_cost: int = 1
</file>

<file path="ability/wall_phase_ability_data.gd.uid">
uid://b5xc2p1kg3ho6
</file>

<file path="ability/wall_phase_system.gd">
class_name WallPhaseSystem
extends Node

@export var tile_query_system: TileQuerySystem
@export var movement_system: MovementSystem
@export var player_service: PlayerService

var _player_node: Node2D
var _paint_component: PaintComponent
var _ability_component: AbilityComponent
var _movement_component: MovementComponent
var _phase_ability: WallPhaseAbilityData

var _is_phasing: bool = false

func _ready() -> void:
    player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node_param: Node2D) -> void:
    _player_node = player_service.get_player_node()
    _paint_component = player_service.get_paint_component()
    _ability_component = player_service.get_ability_component()
    _movement_component = player_service.get_movement_component()

    var phase_ability_node: Ability = _ability_component.get_ability(WallPhaseAbilityData)
    if not is_instance_valid(phase_ability_node):
        return
    _phase_ability = phase_ability_node.data as WallPhaseAbilityData

    if is_instance_valid(_movement_component):
        _movement_component.movement_blocked.connect(_on_movement_blocked)


func _on_movement_blocked(direction: Vector2) -> void:
    if _is_phasing:
        return

    if _paint_component.current_paint < _phase_ability.paint_cost:
        return

    var target_tile: Node2D = _find_passable_tile(direction)
    if not is_instance_valid(target_tile):
        return

    _paint_component.current_paint -= _phase_ability.paint_cost

    _is_phasing = true
    movement_system.teleport(_movement_component, target_tile.global_position)
    _movement_component.movement_completed.connect(
        func() -> void:
            _is_phasing = false,
            CONNECT_ONE_SHOT
    )

func _find_passable_tile(direction: Vector2) -> Node2D:
    var step: int = 8
    if is_instance_valid(_movement_component):
        step = _movement_component.data.tile_size


    var pos: Vector2 = _player_node.global_position + direction * step
    var limit: int = 64
    while limit > 0:
        var tile: Node2D = tile_query_system.get_tile_at_global_pos(pos)
        if is_instance_valid(tile):
            return tile
        pos += direction * step
        limit -= 1
    return null
</file>

<file path="ability/wall_phase_system.gd.uid">
uid://tfbrblckv6u2
</file>

<file path="ability/wall_phase_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://d005t6u2v4w8"]

[ext_resource type="Script" uid="uid://tfbrblckv6u2" path="res://src/ability/wall_phase_system.gd" id="1_system"]

[node name="WallPhaseSystem" type="Node"]
script = ExtResource("1_system")
</file>

<file path="cog/cog_bronze.tscn">
[gd_scene load_steps=5 format=3 uid="uid://v4gsd8opi47x"]

[ext_resource type="PackedScene" uid="uid://cginrjmepqtfq" path="res://src/cog/cog_silver.tscn" id="1_1etis"]
[ext_resource type="Script" uid="uid://clrphpo8v8ep1" path="res://src/time_dilation/time_sensitive_data.gd" id="2_kxw71"]
[ext_resource type="Texture2D" uid="uid://cm7p58sgwcf46" path="res://assets/cog_bronze.png" id="3_2wg6h"]

[sub_resource type="Resource" id="Resource_2wg6h"]
script = ExtResource("2_kxw71")
slow_speed = 16.667
normal_speed = 100.0
slow_rotation_speed = 1.0
normal_rotation_speed = 10.0
metadata/_custom_type_script = "uid://clrphpo8v8ep1"

[node name="CogBronze" instance=ExtResource("1_1etis")]

[node name="TimeSensitiveComponent" parent="." index="0"]
data = SubResource("Resource_2wg6h")

[node name="Sprite2D" parent="." index="2"]
texture = ExtResource("3_2wg6h")
</file>

<file path="cog/cog_silver.tscn">
[gd_scene load_steps=11 format=3 uid="uid://cginrjmepqtfq"]

[ext_resource type="Script" uid="uid://dy4aa6cofjgyh" path="res://src/cog/cog.gd" id="1_sqljv"]
[ext_resource type="Texture2D" uid="uid://dunym6c7vtv8i" path="res://assets/cog_silver.png" id="2_xdquv"]
[ext_resource type="PackedScene" uid="uid://cn0cllvcdbuiy" path="res://src/damage/damage_component.tscn" id="3_mrmkw"]
[ext_resource type="Script" uid="uid://m48gui61bik0" path="res://src/damage/damage_data.gd" id="3_mxeri"]
[ext_resource type="Script" uid="uid://clrphpo8v8ep1" path="res://src/time_dilation/time_sensitive_data.gd" id="4_time_data"]
[ext_resource type="PackedScene" uid="uid://db6e8chc7nyk3" path="res://src/time_dilation/time_sensitive_component.tscn" id="4_time_sensitive"]

[sub_resource type="Resource" id="TimeSensitiveData_abcde"]
script = ExtResource("4_time_data")
slow_speed = 50.0
normal_speed = 300.0
slow_rotation_speed = 1.0
normal_rotation_speed = 10.0

[sub_resource type="Resource" id="Resource_71rs4"]
script = ExtResource("3_mxeri")
amount = 1
cooldown = 1.0
area_path = NodePath("DamageArea")
metadata/_custom_type_script = "uid://m48gui61bik0"

[sub_resource type="CircleShape2D" id="CircleShape2D_xdquv"]
radius = 3.0

[sub_resource type="CircleShape2D" id="CircleShape2D_mxeri"]
radius = 5.0

[node name="CogSilver" type="CharacterBody2D" node_paths=PackedStringArray("sprite", "time_sensitive_component")]
collision_mask = 3
motion_mode = 1
script = ExtResource("1_sqljv")
sprite = NodePath("Sprite2D")
time_sensitive_component = NodePath("TimeSensitiveComponent")

[node name="TimeSensitiveComponent" parent="." instance=ExtResource("4_time_sensitive")]
data = SubResource("TimeSensitiveData_abcde")

[node name="DamageDealerComponent" parent="." instance=ExtResource("3_mrmkw")]
data = SubResource("Resource_71rs4")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("2_xdquv")

[node name="Collision" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_xdquv")

[node name="DamageArea" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="DamageArea"]
position = Vector2(-0.31, 0)
shape = SubResource("CircleShape2D_mxeri")
</file>

<file path="cog/cog.gd">
extends CharacterBody2D

@export var sprite: Sprite2D
@export var time_sensitive_component: TimeSensitiveComponent

var direction: Vector2 = Vector2.ZERO

func _ready() -> void:
	direction = Vector2.RIGHT.rotated(randf_range(0, TAU))

func _physics_process(delta: float) -> void:
	sprite.rotate(time_sensitive_component.current_rotation_speed * delta)

	velocity = direction * time_sensitive_component.current_speed
	var collision: KinematicCollision2D = move_and_collide(velocity * delta)
	if collision:
		direction = direction.bounce(collision.get_normal())
</file>

<file path="cog/cog.gd.uid">
uid://dy4aa6cofjgyh
</file>

<file path="color_tile/color_tile.gd">
class_name ColorTile
extends Node2D

signal painted
signal erased

@export var color_rect: ColorRect

func _ready() -> void:
	add_to_group("tiles")

func paint(color: Color) -> void:
	if is_painted():
		return

	color_rect.color = color
	color_rect.show()

	painted.emit()

func is_painted() -> bool:
	return color_rect.visible

func unpaint() -> void:
	if not is_painted():
		return

	color_rect.hide()
	erased.emit()
</file>

<file path="color_tile/color_tile.gd.uid">
uid://4nvcx314nnga
</file>

<file path="color_tile/color_tile.tscn">
[gd_scene load_steps=5 format=3 uid="uid://2o2nqedmdng0"]

[ext_resource type="Script" uid="uid://4nvcx314nnga" path="res://src/color_tile/color_tile.gd" id="1_82ejh"]
[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="1_dy8m4"]

[sub_resource type="AtlasTexture" id="AtlasTexture_82ejh"]
atlas = ExtResource("1_dy8m4")
region = Rect2(0, 0, 8, 8)

[sub_resource type="CircleShape2D" id="CircleShape2D_dy8m4"]
radius = 3.0

[node name="ColorTile" type="Node2D" node_paths=PackedStringArray("color_rect") groups=["color_tiles"]]
script = ExtResource("1_82ejh")
color_rect = NodePath("ColorRect")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = SubResource("AtlasTexture_82ejh")

[node name="ColorRect" type="ColorRect" parent="."]
visible = false
offset_left = -4.0
offset_top = -4.0
offset_right = 3.0
offset_bottom = 3.0

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("CircleShape2D_dy8m4")
</file>

<file path="damage/damage_component.gd">
class_name DamageDealerComponent
extends Node

@export var data: DamageDealerData
</file>

<file path="damage/damage_component.gd.uid">
uid://cjfeydwjvq7lh
</file>

<file path="damage/damage_component.tscn">
[gd_scene load_steps=2 format=3 uid="uid://cn0cllvcdbuiy"]

[ext_resource type="Script" uid="uid://cjfeydwjvq7lh" path="res://src/damage/damage_component.gd" id="1_lhvkq"]

[node name="DamageDealerComponent" type="Node"]
script = ExtResource("1_lhvkq")
</file>

<file path="damage/damage_data.gd">
class_name DamageDealerData
extends Resource

@export var amount: int = 1
@export var cooldown: float = 0.3
@export var area_path: NodePath
</file>

<file path="damage/damage_data.gd.uid">
uid://m48gui61bik0
</file>

<file path="damage/damage_system.gd">
extends Node

@export var health_system: HealthSystem

var _cooldowns: Dictionary = {}

func _physics_process(delta: float) -> void:
	_tick_cooldowns(delta)
	var dealers: Array[DamageDealerComponent] = _collect_dealers()
	for dealer: DamageDealerComponent in dealers:
		var dealer_data: DamageDealerData = dealer.data
		if dealer_data == null:
			continue
		var area: Area2D = _resolve_area(dealer, dealer_data.area_path)
		if area == null:
			continue
		var areas: Array[Area2D] = area.get_overlapping_areas()
		for a: Area2D in areas:
			_process_hit(dealer, a, dealer_data)

func _process_hit(dealer: DamageDealerComponent, target_node: Node, dealer_data: DamageDealerData) -> void:
	var health_component: HealthComponent = _find_health_component(target_node)
	if health_component == null:
		return
	var key: String = _pair_key(dealer, health_component)
	var left: float = _cooldowns.get(key, 0.0)
	if left > 0.0:
		return
	health_system.apply_damage(health_component, dealer_data.amount)
	_cooldowns[key] = dealer_data.cooldown
	if health_system.is_dead(health_component):
		_invoke_die(health_component)

func _resolve_area(dealer: DamageDealerComponent, path: NodePath) -> Area2D:
	if path.is_empty():
		return null
	var dealer_owner: Node = dealer.get_parent()
	if dealer_owner == null:
		return null
	var n: Node = dealer_owner.get_node_or_null(path)
	if n is Area2D:
		return n as Area2D
	return null

func _find_health_component(node: Node) -> HealthComponent:
	var current: Node = node
	while current != null:
		var children: Array[Node] = current.get_children()
		for c: Node in children:
			if c is HealthComponent:
				return c as HealthComponent
		current = current.get_parent()
	return null

func _collect_dealers() -> Array[DamageDealerComponent]:
	var result: Array[DamageDealerComponent] = []
	var stack: Array[Node] = [get_tree().root]
	while stack.size() > 0:
		var n: Node = stack.pop_back()
		var children: Array[Node] = n.get_children()
		for ch: Node in children:
			stack.push_back(ch)
			if ch is DamageDealerComponent:
				result.append(ch)
	return result

func _pair_key(dealer: DamageDealerComponent, health_component: HealthComponent) -> String:
	return str(dealer.get_instance_id()) + ":" + str(health_component.get_instance_id())

func _tick_cooldowns(delta: float) -> void:
	var keys: Array = _cooldowns.keys()
	for k: String in keys:
		var v: float = _cooldowns[k]
		v -= delta
		if v <= 0.0:
			_cooldowns.erase(k)
		else:
			_cooldowns[k] = v

func _invoke_die(health_component: HealthComponent) -> void:
	var owner_node: Node = health_component.get_parent() if health_component.get_parent() != null else health_component
	if owner_node == null:
		return
	if owner_node.is_queued_for_deletion():
		return
	if owner_node.has_method(&"die"):
		owner_node.call(&"die")
	else:
		push_warning("Узел не имеет метода 'die'")
</file>

<file path="damage/damage_system.gd.uid">
uid://c6uyeqymjyw6d
</file>

<file path="damage/damage_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://br7obebdd1opq"]

[ext_resource type="Script" uid="uid://c6uyeqymjyw6d" path="res://src/damage/damage_system.gd" id="1_n2e5d"]

[node name="DamageSystem" type="Node"]
script = ExtResource("1_n2e5d")
</file>

<file path="dev_tools/dev_tools.gd">
extends Node2D

@export var paint_all_tiles_button: Button
@export var toggle_safe_zone_button: Button
@export var add_inverted_controls_button: Button

const DEBUG_INDICATOR_NAME: StringName = &"DebugSafeTileIndicator"
const SAFE_COLOR := Color(0.0, 1.0, 0.0, 0.9)
const UNSAFE_COLOR := Color(1.0, 0.0, 0.0, 0.9)

var level_progress_system: LevelProgressSystem
var _show_safe_zones: bool = false
var _tile_indicators: Dictionary = {}

func _ready() -> void:
	paint_all_tiles_button.pressed.connect(paint_all_tiles)
	toggle_safe_zone_button.pressed.connect(_on_toggle_safe_zone_pressed)
	add_inverted_controls_button.pressed.connect(_on_add_inverted_controls_pressed)
	_initialize_debug_system.call_deferred()

func _initialize_debug_system() -> void:
	level_progress_system = _find_level_progress_system()
	_create_indicators_for_all_tiles()

func _process(_delta: float) -> void:
	if not _show_safe_zones or not is_instance_valid(level_progress_system):
		return

	var safe_tiles_array: Array[Node2D] = level_progress_system.get_safe_tiles()
	var safe_tiles_set := {}
	for tile in safe_tiles_array:
		safe_tiles_set[tile] = true

	for tile_node: Node2D in _tile_indicators:
		var indicator: ColorRect = _tile_indicators[tile_node]
		if safe_tiles_set.has(tile_node):
			indicator.color = SAFE_COLOR
		else:
			indicator.color = UNSAFE_COLOR

func _on_add_inverted_controls_pressed() -> void:
	var player_node: Node = get_tree().get_first_node_in_group(&"player")
	if not is_instance_valid(player_node):
		return

	var ability_component: AbilityComponent = player_node.find_child(&"AbilityComponent", true, false)
	if not is_instance_valid(ability_component):
		return

	if ability_component.has_ability(InvertedControlsAbilityData):
		return

	var new_ability: InvertedControlsAbilityData = InvertedControlsAbilityData.new()
	ability_component._add_ability(new_ability)

func _on_toggle_safe_zone_pressed() -> void:
	_show_safe_zones = not _show_safe_zones
	if _show_safe_zones:
		_show_all_indicators()
	else:
		_hide_all_indicators()

func _create_indicators_for_all_tiles() -> void:
	var all_tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	for tile in all_tiles:
		var tile_node: Node2D = tile as Node2D
		var indicator := ColorRect.new()
		indicator.name = DEBUG_INDICATOR_NAME
		indicator.size = Vector2(8, 8)
		indicator.position = Vector2(-4, -4)
		indicator.visible = false
		tile_node.add_child(indicator)
		_tile_indicators[tile_node] = indicator

func _show_all_indicators() -> void:
	for indicator: ColorRect in _tile_indicators.values():
		var canvas_item: CanvasItem = indicator
		canvas_item.visible = true

func _hide_all_indicators() -> void:
	for indicator: ColorRect in _tile_indicators.values():
		var canvas_item: CanvasItem = indicator
		canvas_item.visible = false

func _find_level_progress_system() -> LevelProgressSystem:
	var root: Node = get_tree().root
	return _search_for_type(root, LevelProgressSystem) as LevelProgressSystem

func _search_for_type(node: Node, type: Script) -> Node:
	if node.get_script() == type:
		return node

	for child in node.get_children():
		var result: Node = _search_for_type(child, type)
		if result != null:
			return result

	return null

func paint_all_tiles() -> void:
	var tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	for tile in tiles:
		var color_tile: ColorTile = tile as ColorTile
		color_tile.paint(Color.RED)
</file>

<file path="dev_tools/dev_tools.gd.uid">
uid://dg1bgp1q16npr
</file>

<file path="dev_tools/dev_tools.tscn">
[gd_scene load_steps=2 format=3 uid="uid://cnma8j0xdww5"]

[ext_resource type="Script" uid="uid://dg1bgp1q16npr" path="res://src/dev_tools/dev_tools.gd" id="1_nwngj"]

[node name="DevTools" type="Node2D" node_paths=PackedStringArray("paint_all_tiles_button", "toggle_safe_zone_button", "add_inverted_controls_button")]
script = ExtResource("1_nwngj")
paint_all_tiles_button = NodePath("VBoxContainer/PaintAll")
toggle_safe_zone_button = NodePath("VBoxContainer/ToggleSafeZoneButton")
add_inverted_controls_button = NodePath("VBoxContainer/AddInvertedControlsButton")

[node name="VBoxContainer" type="HBoxContainer" parent="."]
offset_right = 222.0
offset_bottom = 66.0

[node name="PaintAll" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "Paint All"

[node name="ToggleSafeZoneButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "Toggle Safe Zones"

[node name="AddInvertedControlsButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "Add Inverted Controls"
</file>

<file path="eraser/eraser.gd">
extends CharacterBody2D

func die() -> void:
	queue_free()
</file>

<file path="eraser/eraser.gd.uid">
uid://bcaxr0585brbo
</file>

<file path="eraser/eraser.tscn">
[gd_scene load_steps=13 format=3 uid="uid://bxy0123eraser"]

[ext_resource type="Script" uid="uid://bcaxr0585brbo" path="res://src/eraser/eraser.gd" id="1_eraser"]
[ext_resource type="Script" uid="uid://c1gtewldstgdg" path="res://src/movement/movement_data.gd" id="3_gq0yk"]
[ext_resource type="PackedScene" uid="uid://k2v1icx8llrc" path="res://src/movement/movement_component.tscn" id="3_movement"]
[ext_resource type="PackedScene" uid="uid://db6e8chc7nyk3" path="res://src/time_dilation/time_sensitive_component.tscn" id="4_time_sensitive"]
[ext_resource type="Script" uid="uid://clrphpo8v8ep1" path="res://src/time_dilation/time_sensitive_data.gd" id="5_time_data"]
[ext_resource type="PackedScene" uid="uid://b2minqce3p1bg" path="res://src/health/health_component.tscn" id="6_health"]
[ext_resource type="Script" uid="uid://chn55a5u5s7ol" path="res://src/health/health_data.gd" id="7_health_data"]
[ext_resource type="Texture2D" uid="uid://bxu67k15ktat" path="res://assets/eraser.png" id="11_gq0yk"]

[sub_resource type="Resource" id="Resource_fnfpw"]
script = ExtResource("3_gq0yk")
tile_size = 8
move_duration = 0.2
metadata/_custom_type_script = "uid://c1gtewldstgdg"

[sub_resource type="Resource" id="Resource_time_data"]
script = ExtResource("5_time_data")
slow_speed = 25.0
normal_speed = 150.0
slow_rotation_speed = 1.0
normal_rotation_speed = 5.0

[sub_resource type="Resource" id="Resource_health_data"]
script = ExtResource("7_health_data")
max_health = 1
current_health = 1

[sub_resource type="CircleShape2D" id="CircleShape2D_eraser"]
radius = 3.0

[node name="Eraser" type="CharacterBody2D"]
collision_mask = 3
motion_mode = 1
script = ExtResource("1_eraser")

[node name="MovementComponent" parent="." node_paths=PackedStringArray("actor", "collision_ray") instance=ExtResource("3_movement")]
data = SubResource("Resource_fnfpw")
actor = NodePath("..")
collision_ray = NodePath("../CollisionRay")

[node name="TimeSensitiveComponent" parent="." instance=ExtResource("4_time_sensitive")]
data = SubResource("Resource_time_data")

[node name="HealthComponent" parent="." instance=ExtResource("6_health")]
data = SubResource("Resource_health_data")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("11_gq0yk")

[node name="Collision" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_eraser")

[node name="Hitbox" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Hitbox"]
shape = SubResource("CircleShape2D_eraser")

[node name="CollisionRay" type="RayCast2D" parent="."]
target_position = Vector2(0, 0)
</file>

<file path="game_progress/game_progress.gd">
extends Node

const SAVE_PATH = "user://progress.dat"

var _completed_levels: Dictionary = {}
var _meta_currency: int = 0
var _stat_upgrades: Dictionary = {
	"max_health": 0,
	"max_paint": 0,
	"move_duration": 0,
	"extra_lives": 0,
	"size_modifier": 0
}

func _ready() -> void:
	load_progress()

func is_level_completed(level_id: String) -> bool:
	return _completed_levels.has(level_id)

func mark_level_as_completed(level_id: String) -> void:
	if not level_id.is_empty():
		_completed_levels[level_id] = true
		save_progress()

func add_meta_currency(amount: int) -> void:
	if amount > 0:
		_meta_currency += amount
		save_progress()

func get_meta_currency() -> int:
	return _meta_currency

func upgrade_stat(stat_name: String, cost: int) -> bool:
	if _meta_currency >= cost and _stat_upgrades.has(stat_name):
		_meta_currency -= cost
		_stat_upgrades[stat_name] += 1
		save_progress()
		return true
	return false

func get_stat_upgrade_level(stat_name: String) -> int:
	return _stat_upgrades.get(stat_name, 0)

func save_progress() -> void:
	var file: FileAccess = FileAccess.open(SAVE_PATH, FileAccess.WRITE)
	if file:
		var save_data: Dictionary = {
			"completed_levels": _completed_levels,
			"meta_currency": _meta_currency,
			"stat_upgrades": _stat_upgrades
		}
		var json_string: String = JSON.stringify(save_data)
		file.store_string(json_string)

func load_progress() -> void:
	if not FileAccess.file_exists(SAVE_PATH):
		return

	var file: FileAccess = FileAccess.open(SAVE_PATH, FileAccess.READ)
	if file:
		var json_string: String = file.get_as_text()
		var parse_result: Variant = JSON.parse_string(json_string)
		if parse_result is Dictionary:
			var save_data: Dictionary = parse_result
			if save_data.has("completed_levels") and save_data["completed_levels"] is Dictionary:
				_completed_levels = save_data["completed_levels"]
			elif parse_result is Dictionary:
				_completed_levels = parse_result

			if save_data.has("meta_currency") and save_data["meta_currency"] is int:
				_meta_currency = save_data["meta_currency"]

			if save_data.has("stat_upgrades") and save_data["stat_upgrades"] is Dictionary:
				_stat_upgrades = save_data["stat_upgrades"]
</file>

<file path="game_progress/game_progress.gd.uid">
uid://ch7a6yof4xj44
</file>

<file path="game_state/game_state_service.gd">
extends Node

enum GameState { HUB, IN_LEVEL, TRANSITIONING }

signal state_changed(new_state: GameState)

var current_state: GameState = GameState.TRANSITIONING

func set_state(new_state: GameState) -> void:
	if current_state == new_state:
		return
	current_state = new_state
	state_changed.emit(new_state)

func get_current_state() -> GameState:
	return current_state

func is_gameplay_active() -> bool:
	return current_state == GameState.IN_LEVEL or current_state == GameState.HUB
</file>

<file path="game_state/game_state_service.gd.uid">
uid://500yflvfb72p
</file>

<file path="health/health_component.gd">
class_name HealthComponent
extends Node

var current_health: int

func initialize() -> void:
	current_health = StatService.get_max_health()
</file>

<file path="health/health_component.gd.uid">
uid://dytikklju1i1t
</file>

<file path="health/health_component.tscn">
[gd_scene load_steps=2 format=3 uid="uid://b2minqce3p1bg"]

[ext_resource type="Script" uid="uid://dytikklju1i1t" path="res://src/health/health_component.gd" id="1_mvbui"]

[node name="HealthComponent" type="Node"]
script = ExtResource("1_mvbui")
</file>

<file path="health/health_data.gd">
class_name HealthData
extends Resource

@export var max_health: int = 1
</file>

<file path="health/health_data.gd.uid">
uid://chn55a5u5s7ol
</file>

<file path="health/health_system.gd">
class_name HealthSystem
extends Node

func set_health(component: HealthComponent, value: int) -> void:
	if not is_instance_valid(component):
		return

	var v: int = value
	if v < 0:
		v = 0
	if v > StatService.get_max_health():
		v = StatService.get_max_health()
	component.current_health = v

func apply_damage(component: HealthComponent, amount: int) -> void:
	set_health(component, component.current_health - amount)

func heal(component: HealthComponent, amount: int) -> void:
	set_health(component, component.current_health + amount)

func is_dead(component: HealthComponent) -> bool:
	return component.current_health <= 0
</file>

<file path="health/health_system.gd.uid">
uid://fqphd7o12dil
</file>

<file path="health/health_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://bl20vi2va8lic"]

[ext_resource type="Script" uid="uid://fqphd7o12dil" path="res://src/health/health_system.gd" id="1_tuuye"]

[node name="HealthSystem" type="Node"]
script = ExtResource("1_tuuye")
</file>

<file path="hub/hub.gd">
class_name Hub
extends BaseLevel

signal start_run

@export var portal: Portal
@export var meta_currency_label: Label

func _ready() -> void:
	super._ready()
	portal.player_entered.connect(_on_portal_player_entered)
	_update_meta_currency_display()

func prepare_scene() -> void:
	tile_query_system.build_map()

func _on_portal_player_entered() -> void:
	start_run.emit()

func _update_meta_currency_display() -> void:
	var currency: int = GameProgress.get_meta_currency()
	meta_currency_label.text = "Мета-валюта: " + str(currency)
</file>

<file path="hub/hub.gd.uid">
uid://bxnlwxu5qs7lf
</file>

<file path="hub/hub.tscn">
[gd_scene load_steps=14 format=4 uid="uid://bqx8h4n5m2k7p"]

[ext_resource type="Script" uid="uid://bxnlwxu5qs7lf" path="res://src/hub/hub.gd" id="1_hub"]
[ext_resource type="PackedScene" uid="uid://c8lam3n4p5q6r" path="res://src/hub/portal.tscn" id="3_portal"]
[ext_resource type="PackedScene" uid="uid://2o2nqedmdng0" path="res://src/color_tile/color_tile.tscn" id="4_lgcv4"]
[ext_resource type="PackedScene" uid="uid://dfy35tmjoeoht" path="res://src/water_tile/water_tile.tscn" id="5_gb5ag"]
[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="6_0pbk3"]
[ext_resource type="PackedScene" uid="uid://dmr0fcamx7t56" path="res://addons/virtual_joystick/virtual_joystick_scene.tscn" id="11_wj811"]
[ext_resource type="Script" uid="uid://cndyjg0c5r3k4" path="res://src/player/player_service.gd" id="18_df2jy"]
[ext_resource type="PackedScene" uid="uid://biw4o1x1u6l4i" path="res://src/tile_query_system/tile_query_system.tscn" id="18_mmg22"]
[ext_resource type="PackedScene" uid="uid://bi0dhs7jf7yrl" path="res://src/movement/movement_system.tscn" id="19_m8kwa"]
[ext_resource type="PackedScene" uid="uid://5agsxx7ekdi6" path="res://src/player/player_input_system.tscn" id="20_wlut3"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_lgcv4"]
scenes/1/scene = ExtResource("4_lgcv4")
scenes/2/scene = ExtResource("5_gb5ag")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_gb5ag"]
texture = ExtResource("6_0pbk3")
texture_region_size = Vector2i(8, 8)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:0/0 = 0
0:1/0 = 0
0:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
1:1/0 = 0
1:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
2:1/0 = 0
2:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:1/0 = 0
3:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)

[sub_resource type="TileSet" id="TileSet_0pbk3"]
tile_size = Vector2i(8, 8)
physics_layer_0/collision_layer = 1
physics_layer_1/collision_layer = 2
physics_layer_1/collision_mask = 0
sources/1 = SubResource("TileSetAtlasSource_gb5ag")
sources/0 = SubResource("TileSetScenesCollectionSource_lgcv4")

[node name="Hub" type="Node2D" node_paths=PackedStringArray("portal", "meta_currency_label", "tile_query_system", "player_service")]
scale = Vector2(8, 8)
script = ExtResource("1_hub")
portal = NodePath("Portal")
meta_currency_label = NodePath("CanvasLayer/MetaCurrencyLabel")
tile_query_system = NodePath("Systems/TileQuerySystem")
player_service = NodePath("Services/PlayerService")

[node name="TileMapLayer" type="TileMapLayer" parent="."]
texture_filter = 1
position = Vector2(0, 22.5)
tile_map_data = PackedByteArray("AAAAAAgAAQACAAAAAAAAAAkAAQACAAAAAAAAAAoAAQACAAAAAAAAAAsAAQACAAAAAAAAAAwAAQACAAAAAAAAAA0AAQACAAAAAAAAAA4AAQACAAAAAAAAAA8AAQACAAAAAAAAABAAAQACAAAAAAAAABEAAQACAAAAAAAAABIAAQACAAAAAAAAABMAAQACAAAAAAAAABQAAQACAAAAAAAAABUAAQACAAAAAAABAA0AAQACAAAAAAABAA4AAQACAAAAAAABABUAAQACAAAAAAACABUAAQACAAAAAAADABUAAQACAAAAAAAEABUAAQACAAAAAAAFABUAAQACAAAAAAAGABUAAQACAAAAAAAHAAgAAAAAAAAAAQAHAAkAAAAAAAAAAQAHABIAAAAAAAAAAQAHABUAAQACAAAAAAAIAAgAAAAAAAAAAQAIABIAAAAAAAAAAQAIABUAAQACAAAAAAAJAAgAAAAAAAAAAQAJABUAAQACAAAAAAAKABUAAQACAAAAAAALABUAAQACAAAAAAAMABUAAQACAAAAAAANABUAAQACAAAAAAAOABUAAQACAAAAAAAPAA0AAQACAAAAAAAPAA4AAQACAAAAAAAPABUAAQACAAAAAAAQAAgAAQACAAAAAAAQAAkAAQACAAAAAAAQAAoAAQACAAAAAAAQAAsAAQACAAAAAAAQAAwAAQACAAAAAAAQAA0AAQACAAAAAAAQAA4AAQACAAAAAAAQAA8AAQACAAAAAAAQABAAAQACAAAAAAAQABEAAQACAAAAAAAQABIAAQACAAAAAAAQABMAAQACAAAAAAAQABQAAQACAAAAAAAQABUAAQACAAAAAAAAAAYAAQACAAAAAAAAAAcAAQACAAAAAAABAAYAAQACAAAAAAACAAYAAQACAAAAAAADAAYAAQACAAAAAAAEAAYAAQACAAAAAAAFAAYAAQACAAAAAAAGAAYAAQACAAAAAAAHAAYAAQACAAAAAAAIAAYAAQACAAAAAAAJAAYAAQACAAAAAAAKAAYAAQACAAAAAAALAAYAAQACAAAAAAAMAAYAAQACAAAAAAANAAYAAQACAAAAAAAOAAYAAQACAAAAAAAPAAYAAQACAAAAAAAQAAYAAQACAAAAAAAQAAcAAQACAAAAAAALABIAAAAAAAAAAQALABEAAAAAAAAAAQALABAAAAAAAAAAAQALAA8AAAAAAAAAAQALAA4AAAAAAAAAAQALAA0AAAAAAAAAAQALAAwAAAAAAAAAAQALAAsAAAAAAAAAAQALAAoAAAAAAAAAAQALAAkAAAAAAAAAAQALAAgAAAAAAAAAAQALAAcAAAAAAAAAAQAKAAcAAAAAAAAAAQAJAAcAAAAAAAAAAQAIAAcAAAAAAAAAAQAHAAcAAAAAAAAAAQAGAAcAAAAAAAAAAQAFAAcAAAAAAAAAAQAEAAcAAAAAAAAAAQADAAcAAAAAAAAAAQACAAcAAAAAAAAAAQABAAcAAAAAAAAAAQABAAgAAAAAAAAAAQABAAkAAAAAAAAAAQABAAoAAAAAAAAAAQABAAsAAAAAAAAAAQABAAwAAAAAAAAAAQACAAwAAAAAAAAAAQACAAsAAAAAAAAAAQACAAoAAAAAAAAAAQACAAkAAAAAAAAAAQACAAgAAAAAAAAAAQADAAgAAAAAAAAAAQADAAkAAAAAAAAAAQADAAoAAAAAAAAAAQADAAsAAAAAAAAAAQADAAwAAAAAAAAAAQADAA0AAAAAAAAAAQACAA0AAAAAAAAAAQACAA4AAAAAAAAAAQACAA8AAAAAAAAAAQABAA8AAAAAAAAAAQABABAAAAAAAAAAAQABABEAAAAAAAAAAQABABIAAAAAAAAAAQABABMAAAAAAAAAAQABABQAAAAAAAAAAQACABQAAAAAAAAAAQACABMAAAAAAAAAAQACABIAAAAAAAAAAQACABEAAAAAAAAAAQACABAAAAAAAAAAAQADABAAAAAAAAAAAQADAA8AAAAAAAAAAQADAA4AAAAAAAAAAQAEAA4AAAAAAAAAAQAEAA0AAAAAAAAAAQAEAAwAAAAAAAAAAQAEAAsAAAAAAAAAAQAEAAoAAAAAAAAAAQAEAAkAAAAAAAAAAQAEAAgAAAAAAAAAAQAFAAgAAAAAAAAAAQAFAAkAAAAAAAAAAQAFAAoAAAAAAAAAAQAFAAsAAAAAAAAAAQAFAAwAAAAAAAAAAQAFAA0AAAAAAAAAAQAFAA4AAAAAAAAAAQAFAA8AAAAAAAAAAQAEAA8AAAAAAAAAAQAEABAAAAAAAAAAAQAEABEAAAAAAAAAAQADABEAAAAAAAAAAQADABIAAAAAAAAAAQADABMAAAAAAAAAAQADABQAAAAAAAAAAQAEABQAAAAAAAAAAQAEABMAAAAAAAAAAQAEABIAAAAAAAAAAQAFABIAAAAAAAAAAQAFABEAAAAAAAAAAQAFABAAAAAAAAAAAQAGABAAAAAAAAAAAQAGAA8AAAAAAAAAAQAGAA4AAAAAAAAAAQAGAA0AAAAAAAAAAQAGAAwAAAAAAAAAAQAGAAsAAAAAAAAAAQAGAAoAAAAAAAAAAQAGAAkAAAAAAAAAAQAGAAgAAAAAAAAAAQAHAAoAAAAAAAAAAQAHAAsAAAAAAAAAAQAHAAwAAAAAAAAAAQAHAA0AAAAAAAAAAQAHAA4AAAAAAAAAAQAHAA8AAAAAAAAAAQAHABAAAAAAAAAAAQAHABEAAAAAAAAAAQAGABEAAAAAAAAAAQAGABIAAAAAAAAAAQAGABMAAAAAAAAAAQAFABMAAAAAAAAAAQAFABQAAAAAAAAAAQAGABQAAAAAAAAAAQAHABQAAAAAAAAAAQAHABMAAAAAAAAAAQAIABMAAAAAAAAAAQAIABQAAAAAAAAAAQAJABQAAAAAAAAAAQAJABMAAAAAAAAAAQAJABIAAAAAAAAAAQAJABEAAAAAAAAAAQAJABAAAAAAAAAAAQAJAA8AAAAAAAAAAQAJAA4AAAAAAAAAAQAJAA0AAAAAAAAAAQAJAAwAAAAAAAAAAQAJAAsAAAAAAAAAAQAJAAoAAAAAAAAAAQAJAAkAAAAAAAAAAQAIAAkAAAAAAAAAAQAIAAoAAAAAAAAAAQAIAAsAAAAAAAAAAQAIAAwAAAAAAAAAAQAIAA0AAAAAAAAAAQAIAA4AAAAAAAAAAQAIAA8AAAAAAAAAAQAIABAAAAAAAAAAAQAIABEAAAAAAAAAAQAKAAkAAAAAAAAAAQAKAAgAAAAAAAAAAQAKAAoAAAAAAAAAAQAKAAsAAAAAAAAAAQAKAAwAAAAAAAAAAQAKAA0AAAAAAAAAAQAKAA4AAAAAAAAAAQAKAA8AAAAAAAAAAQAKABAAAAAAAAAAAQAKABEAAAAAAAAAAQAKABIAAAAAAAAAAQAKABMAAAAAAAAAAQAKABQAAAAAAAAAAQALABQAAAAAAAAAAQALABMAAAAAAAAAAQAMABMAAAAAAAAAAQAMABIAAAAAAAAAAQAMABEAAAAAAAAAAQAMABAAAAAAAAAAAQAMAA8AAAAAAAAAAQAMAA4AAAAAAAAAAQAMAA0AAAAAAAAAAQAMAAwAAAAAAAAAAQAMAAsAAAAAAAAAAQAMAAoAAAAAAAAAAQAMAAkAAAAAAAAAAQAMAAgAAAAAAAAAAQAMAAcAAAAAAAAAAQANAAcAAAAAAAAAAQANAAgAAAAAAAAAAQANAAkAAAAAAAAAAQANAAoAAAAAAAAAAQANAAsAAAAAAAAAAQANAAwAAAAAAAAAAQANAA0AAAAAAAAAAQANAA4AAAAAAAAAAQANAA8AAAAAAAAAAQANABAAAAAAAAAAAQANABEAAAAAAAAAAQANABIAAAAAAAAAAQANABMAAAAAAAAAAQANABQAAAAAAAAAAQAMABQAAAAAAAAAAQAOABQAAAAAAAAAAQAOABMAAAAAAAAAAQAOABIAAAAAAAAAAQAOABEAAAAAAAAAAQAOABAAAAAAAAAAAQAOAA8AAAAAAAAAAQAOAA4AAAAAAAAAAQAOAA0AAAAAAAAAAQAOAAwAAAAAAAAAAQAOAAsAAAAAAAAAAQAOAAoAAAAAAAAAAQAOAAkAAAAAAAAAAQAOAAgAAAAAAAAAAQAOAAcAAAAAAAAAAQAPAAcAAAAAAAAAAQAPAAgAAAAAAAAAAQAPAAkAAAAAAAAAAQAPAAoAAAAAAAAAAQAPAAsAAAAAAAAAAQAPAAwAAAAAAAAAAQAPAA8AAAAAAAAAAQAPABAAAAAAAAAAAQAPABEAAAAAAAAAAQAPABIAAAAAAAAAAQAPABMAAAAAAAAAAQAPABQAAAAAAAAAAQA=")
tile_set = SubResource("TileSet_0pbk3")

[node name="Portal" parent="." instance=ExtResource("3_portal")]
position = Vector2(59.875, 137.375)

[node name="Services" type="Node" parent="."]

[node name="PlayerService" type="Node" parent="Services"]
script = ExtResource("18_df2jy")
metadata/_custom_type_script = "uid://cndyjg0c5r3k4"

[node name="Systems" type="Node" parent="."]

[node name="TileQuerySystem" parent="Systems" instance=ExtResource("18_mmg22")]

[node name="MovementSystem" parent="Systems" node_paths=PackedStringArray("player_service", "tile_query_system") instance=ExtResource("19_m8kwa")]
player_service = NodePath("../../Services/PlayerService")
tile_query_system = NodePath("../TileQuerySystem")

[node name="PlayerInputSystem" parent="Systems" node_paths=PackedStringArray("movement_system", "player_service") instance=ExtResource("20_wlut3")]
movement_system = NodePath("../MovementSystem")
player_service = NodePath("../../Services/PlayerService")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="MetaCurrencyLabel" type="Label" parent="CanvasLayer"]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -50.0
offset_right = 300.0
offset_bottom = -20.0
text = "Мета-валюта: 0"

[node name="Virtual Joystick" parent="CanvasLayer" instance=ExtResource("11_wj811")]
anchors_preset = 15
anchor_top = 0.0
anchor_right = 1.0
offset_top = 0.0
offset_right = 0.0
offset_bottom = 0.0
grow_horizontal = 2
grow_vertical = 2
deadzone_size = 40.0
joystick_mode = 2
visibility_mode = 3
action_left = "move_left"
action_right = "move_right"
action_up = "move_up"
action_down = "move_down"

[editable path="CanvasLayer/Virtual Joystick"]
</file>

<file path="hub/portal.gd">
class_name Portal
extends Area2D

signal player_entered

func _ready() -> void:
	body_entered.connect(_on_body_entered)

func _on_body_entered(body: Node2D) -> void:
	if body.is_in_group("player"):
		player_entered.emit()
</file>

<file path="hub/portal.gd.uid">
uid://8bxu26fng2a6
</file>

<file path="hub/portal.tscn">
[gd_scene load_steps=4 format=3 uid="uid://c8lam3n4p5q6r"]

[ext_resource type="Script" uid="uid://8bxu26fng2a6" path="res://src/hub/portal.gd" id="1_portal"]
[ext_resource type="Texture2D" uid="uid://dxvmoabu0ee3q" path="res://assets/portal.png" id="2_eat10"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(3, 3)

[node name="Portal" type="Area2D"]
script = ExtResource("1_portal")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.5, 0.8, 1, 0.8)
texture_filter = 1
texture = ExtResource("2_eat10")
</file>

<file path="level/conditions/all_players_dead_condition_data.gd">
class_name AllPlayersDeadConditionData
extends ConditionData
</file>

<file path="level/conditions/all_players_dead_condition_data.gd.uid">
uid://2ec5m4habqbs
</file>

<file path="level/conditions/all_players_dead_condition_system.gd">
class_name AllPlayersDeadConditionSystem
extends ConditionSystem

@export var health_system: HealthSystem
@export var player_service: PlayerService

func is_met() -> bool:
	var health_component: HealthComponent = player_service.get_health_component()
	if not is_instance_valid(health_component):
		return true

	return health_system.is_dead(health_component)
</file>

<file path="level/conditions/all_players_dead_condition_system.gd.uid">
uid://dou1pba0f1mb3
</file>

<file path="level/conditions/condition_data.gd">
class_name ConditionData
extends Resource
</file>

<file path="level/conditions/condition_data.gd.uid">
uid://cip4vgi18doym
</file>

<file path="level/conditions/condition_system.gd">
class_name ConditionSystem
extends Node

func is_met() -> bool:
	push_error("is_met() must be implemented in a subclass of ConditionSystem.")
	return false
</file>

<file path="level/conditions/condition_system.gd.uid">
uid://c53gk7fdxt2a1
</file>

<file path="level/conditions/loss_by_death.tres">
[gd_resource type="Resource" script_class_name="AllPlayersDeadConditionData" load_steps=2 format=3 uid="uid://c9d0e1f2g3h4i5j6k7l8m9n0o1p2q3r4"]

[ext_resource type="Script" path="res://src/level/conditions/all_players_dead_condition_data.gd" id="1_hijklmn"]

[resource]
script = ExtResource("1_hijklmn")
</file>

<file path="level/conditions/paint_all_tiles_condition_data.gd">
class_name PaintAllTilesConditionData
extends ConditionData
</file>

<file path="level/conditions/paint_all_tiles_condition_data.gd.uid">
uid://4eqgqqg250k0
</file>

<file path="level/conditions/paint_all_tiles_condition_system.gd">
class_name PaintAllTilesConditionSystem
extends ConditionSystem

@export var level_state_component: LevelStateComponent

func is_met() -> bool:
	var data: LevelStateData = level_state_component.data
	return data.painted_tiles >= data.total_tiles and data.total_tiles > 0
</file>

<file path="level/conditions/paint_all_tiles_condition_system.gd.uid">
uid://daib4adyoymm7
</file>

<file path="level/conditions/win_by_painting.tres">
[gd_resource type="Resource" script_class_name="PaintAllTilesConditionData" load_steps=2 format=3 uid="uid://b8m4x1q2r3s5t6u7v8w9x0y1z2a3b4c5"]

[ext_resource type="Script" path="res://src/level/conditions/paint_all_tiles_condition_data.gd" id="1_abcdefg"]

[resource]
script = ExtResource("1_abcdefg")
level_state_component_path = NodePath("../LevelStateComponent")
</file>

<file path="level/outcome_system/level_outcome_system.gd">
extends Node

signal win_condition_met

@export var level_state_component: LevelStateComponent
@export var win_condition_systems: Array[ConditionSystem]
@export var loss_condition_systems: Array[ConditionSystem]

func _process(_delta: float) -> void:
	if not GameStateService.is_gameplay_active():
		return

	var data: LevelStateData = level_state_component.data
	if data.state != LevelStateData.State.PLAYING:
		return

	var all_win_conditions_met: bool = true
	if win_condition_systems.is_empty():
		all_win_conditions_met = false
	else:
		for system: ConditionSystem in win_condition_systems:
			if not system.is_met():
				all_win_conditions_met = false
				break

	if all_win_conditions_met:
		data.state = LevelStateData.State.WON
		win_condition_met.emit()
		return

	for system: ConditionSystem in loss_condition_systems:
		if system.is_met():
			data.state = LevelStateData.State.LOST
			return
</file>

<file path="level/outcome_system/level_outcome_system.gd.uid">
uid://due7bqehu4dje
</file>

<file path="level/outcome_system/level_outcome_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://dawp704yypk8x"]

[ext_resource type="Script" uid="uid://due7bqehu4dje" path="res://src/level/outcome_system/level_outcome_system.gd" id="1_0d356"]

[node name="LevelOutcomeSystem" type="Node"]
script = ExtResource("1_0d356")
</file>

<file path="level/progress_system/level_event_data.gd">
class_name LevelEventData
extends Resource

## Прогресс от 0.0 до 1.0, при котором должно сработать событие.
@export var progress_threshold: float = 0.0

## Сцена, которую нужно заспавнить.
@export var scene_to_spawn: PackedScene

## Иконка для отображения на прогресс-баре.
@export var icon: Texture2D
</file>

<file path="level/progress_system/level_event_data.gd.uid">
uid://d22krovyllkp2
</file>

<file path="level/progress_system/level_progress_system.gd">
class_name LevelProgressSystem
extends Node

@export var level_state_component: LevelStateComponent
@export var level: Level
@export var min_spawn_distance_from_player: float = 40.0
@export var player_service: PlayerService
@export var exit_portal_scene: PackedScene

func _ready() -> void:
	level_state_component.data.reset()

func _process(_delta: float) -> void:
	var data: LevelStateData = level_state_component.data
	if data.state != LevelStateData.State.PLAYING or data.events.is_empty():
		return

	var current_progress: float = data.get_progress()

	for i in range(data.events.size()):
		var event: LevelEventData = data.events[i]
		var is_triggered: bool = data._triggered_events[i]

		if not is_triggered and current_progress >= event.progress_threshold:
			_trigger_event(event)
			data.trigger_event(i)

func get_safe_tiles() -> Array[Node2D]:
	var safe_tiles: Array[Node2D] = []
	var player_node: Node2D = player_service.get_player_node()
	if not is_instance_valid(player_node):
		return safe_tiles

	var all_tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	var player_position: Vector2 = player_node.global_position

	for tile in all_tiles:
		var tile_node: Node2D = tile as Node2D
		if tile_node.global_position.distance_to(player_position) > min_spawn_distance_from_player:
			safe_tiles.append(tile_node)
	return safe_tiles

func _trigger_event(event_data: LevelEventData) -> void:
	if event_data.scene_to_spawn != null:
		_spawn_scene_at_safe_tile(event_data.scene_to_spawn)

func _spawn_scene_at_safe_tile(scene: PackedScene) -> void:
	if level == null:
		return

	var safe_tiles: Array[Node2D] = get_safe_tiles()
	if safe_tiles.is_empty():
		push_warning("LevelProgressSystem: No safe tiles found to spawn scene.")
		return

	var random_tile: Node2D = safe_tiles.pick_random()
	var spawn_position: Vector2 = random_tile.global_position

	var new_instance: Node2D = scene.instantiate() as Node2D
	level.add_child(new_instance)
	new_instance.global_position = spawn_position

func on_win_condition_met() -> void:
	var safe_tiles: Array[Node2D] = get_safe_tiles()
	if safe_tiles.is_empty():
		var player_node: Node2D = player_service.get_player_node()
		_spawn_portal_at(player_node.global_position)
	else:
		var random_tile: Node2D = safe_tiles.pick_random()
		_spawn_portal_at(random_tile.global_position)

	_mark_level_as_completed()

func _spawn_portal_at(position: Vector2) -> void:
	var portal_instance: Portal = exit_portal_scene.instantiate()
	level.add_child(portal_instance)
	portal_instance.global_position = position
	portal_instance.player_entered.connect(_on_player_entered_portal)

func _on_player_entered_portal() -> void:
	level.level_completed.emit()

func _mark_level_as_completed() -> void:
	GameProgress.mark_level_as_completed(level.level_id)
</file>

<file path="level/progress_system/level_progress_system.gd.uid">
uid://bbdyeamyi30l2
</file>

<file path="level/progress_system/level_progress_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://cg0xsxrffpvqs"]

[ext_resource type="Script" uid="uid://bbdyeamyi30l2" path="res://src/level/progress_system/level_progress_system.gd" id="1_abcde"]

[node name="LevelProgressSystem" type="Node"]
script = ExtResource("1_abcde")
</file>

<file path="level/base_level.gd">
class_name BaseLevel
extends Node2D

signal ready_for_spawn

@export var tile_query_system: TileQuerySystem
@export var player_service: PlayerService

func _ready() -> void:
	ready_for_spawn.emit.call_deferred()

func prepare_scene() -> void:
	pass

func setup_and_spawn_player(player_scene: PackedScene) -> void:
	var spawn_tile: Node2D = tile_query_system.get_random_tile()
	if not is_instance_valid(spawn_tile):
		push_error("BaseLevel: No spawnable tiles found.")
		return

	var player_instance: Node2D = player_scene.instantiate() as Node2D
	if not is_instance_valid(player_instance):
		push_error("BaseLevel: Failed to instantiate player scene.")
		return

	add_child(player_instance)
	player_instance.global_position = spawn_tile.global_position

	player_service.initialize(player_instance)
</file>

<file path="level/base_level.gd.uid">
uid://cdx3goyxc18pw
</file>

<file path="level/level_state_component.gd">
class_name LevelStateComponent
extends Node

@export var data: LevelStateData
</file>

<file path="level/level_state_component.gd.uid">
uid://dcoacmm8gxq0j
</file>

<file path="level/level_state_component.tscn">
[gd_scene load_steps=2 format=3 uid="uid://dg3necnhp81wv"]

[ext_resource type="Script" uid="uid://dcoacmm8gxq0j" path="res://src/level/level_state_component.gd" id="1_2bpcy"]

[node name="LevelStateComponent" type="Node"]
script = ExtResource("1_2bpcy")
</file>

<file path="level/level_state_data.gd">
class_name LevelStateData
extends Resource

enum State {PLAYING, WON, LOST}

signal state_changed(new_state: State)
signal progress_changed(new_progress: float)
signal event_triggered(event_index: int)

@export var events: Array[LevelEventData]

var _triggered_events: Array[bool]

var total_tiles: int = 0
var state: State = State.PLAYING:
	set(new_state):
		if state == new_state:
			return
		state = new_state
		state_changed.emit(state)

var painted_tiles: int = 0:
	set(new_painted_count):
		if painted_tiles == new_painted_count:
			return
		painted_tiles = new_painted_count
		progress_changed.emit(get_progress())

func get_progress() -> float:
	if total_tiles <= 0:
		return 0.0
	return float(painted_tiles) / float(total_tiles)

func trigger_event(index: int) -> void:
	if index < 0 or index >= _triggered_events.size():
		return
	_triggered_events[index] = true
	event_triggered.emit(index)

func reset() -> void:
	total_tiles = 0
	painted_tiles = 0
	state = State.PLAYING

	if not events.is_empty():
		_triggered_events.resize(events.size())
		_triggered_events.fill(false)
</file>

<file path="level/level_state_data.gd.uid">
uid://togcg2ol5h6u
</file>

<file path="level/level.gd">
class_name Level
extends BaseLevel

signal level_completed
signal level_failed

@export var level_id: String = ""
@export var level_state_component: LevelStateComponent
@export var loss_ui: CanvasLayer

func _ready() -> void:
	super._ready()
	level_state_component.data.state_changed.connect(_on_level_state_changed)

	if loss_ui.has_signal("play_again_pressed"):
		loss_ui.connect("play_again_pressed", _on_level_failed)

func prepare_scene() -> void:
	tile_query_system.build_map()
	_setup_level()

func _setup_level() -> void:
	var tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	level_state_component.data.total_tiles = tiles.size()

	for tile_node: ColorTile in tiles:
		if tile_node is ColorTile:
			tile_node.painted.connect(_on_tile_painted)


func _on_level_state_changed(new_state: LevelStateData.State) -> void:
	if new_state == LevelStateData.State.LOST:
		loss_ui.show()

func _on_tile_painted() -> void:
	level_state_component.data.painted_tiles += 1

func _on_level_completed() -> void:
	level_completed.emit()

func _on_level_failed() -> void:
	level_failed.emit()
</file>

<file path="level/level.gd.uid">
uid://b5vf4g4ieswao
</file>

<file path="level/level.tscn">
[gd_scene load_steps=42 format=4 uid="uid://bk5rydqrj5etw"]

[ext_resource type="Script" uid="uid://b5vf4g4ieswao" path="res://src/level/level.gd" id="1_5vox1"]
[ext_resource type="PackedScene" uid="uid://2o2nqedmdng0" path="res://src/color_tile/color_tile.tscn" id="2_5vox1"]
[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="3_ob0n7"]
[ext_resource type="PackedScene" uid="uid://ce5u8lunfmkij" path="res://src/paint_bar/paint_bar.tscn" id="4_p6v4a"]
[ext_resource type="Script" uid="uid://d22krovyllkp2" path="res://src/level/progress_system/level_event_data.gd" id="4_y4oqq"]
[ext_resource type="PackedScene" uid="uid://bv3kc8uwrke6f" path="res://src/progress_bar/progress_bar.tscn" id="5_5suky"]
[ext_resource type="Texture2D" uid="uid://dunym6c7vtv8i" path="res://assets/cog_silver.png" id="5_u1qsv"]
[ext_resource type="Script" uid="uid://daib4adyoymm7" path="res://src/level/conditions/paint_all_tiles_condition_system.gd" id="7_gj0ym"]
[ext_resource type="PackedScene" uid="uid://c8lam3n4p5q6r" path="res://src/hub/portal.tscn" id="7_portal"]
[ext_resource type="Texture2D" uid="uid://cm7p58sgwcf46" path="res://assets/cog_bronze.png" id="7_vgudg"]
[ext_resource type="Script" uid="uid://dou1pba0f1mb3" path="res://src/level/conditions/all_players_dead_condition_system.gd" id="8_5yot2"]
[ext_resource type="PackedScene" uid="uid://bl20vi2va8lic" path="res://src/health/health_system.tscn" id="8_n4rap"]
[ext_resource type="PackedScene" uid="uid://cginrjmepqtfq" path="res://src/cog/cog_silver.tscn" id="9_p6v4a"]
[ext_resource type="PackedScene" uid="uid://b2qufv2y580sm" path="res://src/loss_ui/loss_ui.tscn" id="10_5suky"]
[ext_resource type="PackedScene" uid="uid://dg3necnhp81wv" path="res://src/level/level_state_component.tscn" id="10_k5trb"]
[ext_resource type="PackedScene" uid="uid://br7obebdd1opq" path="res://src/damage/damage_system.tscn" id="11_iv48k"]
[ext_resource type="Script" uid="uid://togcg2ol5h6u" path="res://src/level/level_state_data.gd" id="13_n4rap"]
[ext_resource type="PackedScene" uid="uid://dawp704yypk8x" path="res://src/level/outcome_system/level_outcome_system.tscn" id="15_5lobc"]
[ext_resource type="PackedScene" uid="uid://cwq6ex0lmj7al" path="res://src/paint/paint_system.tscn" id="16_paint_system"]
[ext_resource type="PackedScene" uid="uid://e0dt5jwwxndr" path="res://src/time_dilation/time_dilation_system.tscn" id="16_ww2lt"]
[ext_resource type="PackedScene" uid="uid://bi0dhs7jf7yrl" path="res://src/movement/movement_system.tscn" id="18_cwwql"]
[ext_resource type="PackedScene" uid="uid://se1apgxlfmx6" path="res://src/paint_refill/paint_refill_drop.tscn" id="18_paint_refill_drop"]
[ext_resource type="PackedScene" uid="uid://bnbdlymwq0s8q" path="res://src/water/water_system.tscn" id="19_cwwql"]
[ext_resource type="PackedScene" uid="uid://5agsxx7ekdi6" path="res://src/player/player_input_system.tscn" id="19_kd75s"]
[ext_resource type="PackedScene" uid="uid://cg0xsxrffpvqs" path="res://src/level/progress_system/level_progress_system.tscn" id="19_level_progress_system"]
[ext_resource type="PackedScene" uid="uid://dfy35tmjoeoht" path="res://src/water_tile/water_tile.tscn" id="21_kd75s"]
[ext_resource type="PackedScene" uid="uid://biw4o1x1u6l4i" path="res://src/tile_query_system/tile_query_system.tscn" id="23_ww2lt"]
[ext_resource type="Script" uid="uid://cndyjg0c5r3k4" path="res://src/player/player_service.gd" id="24_s7x28"]
[ext_resource type="PackedScene" uid="uid://d005t6u2v4w8" path="res://src/ability/wall_phase_system.tscn" id="24_wallphase"]
[ext_resource type="PackedScene" uid="uid://cne4tjc17alt0" path="res://src/ability/inverted_controls_system.tscn" id="25_inverted_controls"]
[ext_resource type="PackedScene" uid="uid://v4gsd8opi47x" path="res://src/cog/cog_bronze.tscn" id="29_o5bw5"]
[ext_resource type="PackedScene" uid="uid://cnma8j0xdww5" path="res://src/dev_tools/dev_tools.tscn" id="32_bgdgy"]
[ext_resource type="PackedScene" uid="uid://dmr0fcamx7t56" path="res://addons/virtual_joystick/virtual_joystick_scene.tscn" id="33_s7x28"]

[sub_resource type="Resource" id="Resource_y4oqq"]
script = ExtResource("4_y4oqq")
progress_threshold = 0.3
scene_to_spawn = ExtResource("9_p6v4a")
icon = ExtResource("5_u1qsv")
metadata/_custom_type_script = "uid://d22krovyllkp2"

[sub_resource type="Resource" id="Resource_ww2lt"]
script = ExtResource("4_y4oqq")
progress_threshold = 0.6
scene_to_spawn = ExtResource("9_p6v4a")
icon = ExtResource("5_u1qsv")
metadata/_custom_type_script = "uid://d22krovyllkp2"

[sub_resource type="Resource" id="Resource_gj0ym"]
script = ExtResource("4_y4oqq")
progress_threshold = 0.9
scene_to_spawn = ExtResource("9_p6v4a")
icon = ExtResource("5_u1qsv")
metadata/_custom_type_script = "uid://d22krovyllkp2"

[sub_resource type="Resource" id="Resource_5yot2"]
script = ExtResource("4_y4oqq")
progress_threshold = 0.05
scene_to_spawn = ExtResource("29_o5bw5")
icon = ExtResource("7_vgudg")
metadata/_custom_type_script = "uid://d22krovyllkp2"

[sub_resource type="Resource" id="Resource_ascnc"]
script = ExtResource("13_n4rap")
events = Array[ExtResource("4_y4oqq")]([SubResource("Resource_y4oqq"), SubResource("Resource_ww2lt"), SubResource("Resource_gj0ym"), SubResource("Resource_5yot2")])
metadata/_custom_type_script = "uid://togcg2ol5h6u"

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_s7x28"]
scenes/1/scene = ExtResource("2_5vox1")
scenes/2/scene = ExtResource("21_kd75s")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_1wj5g"]
texture = ExtResource("3_ob0n7")
texture_region_size = Vector2i(8, 8)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:0/0 = 0
0:1/0 = 0
0:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
1:1/0 = 0
1:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
2:1/0 = 0
2:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:1/0 = 0
3:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)

[sub_resource type="TileSet" id="TileSet_rccf1"]
tile_size = Vector2i(8, 8)
physics_layer_0/collision_layer = 1
physics_layer_1/collision_layer = 2
physics_layer_1/collision_mask = 0
sources/1 = SubResource("TileSetAtlasSource_1wj5g")
sources/0 = SubResource("TileSetScenesCollectionSource_s7x28")

[node name="Level" type="Node2D" node_paths=PackedStringArray("level_state_component", "loss_ui", "tile_query_system", "player_service")]
scale = Vector2(8, 8)
script = ExtResource("1_5vox1")
level_state_component = NodePath("Systems/LevelOutcomeSystem/LevelStateComponent")
loss_ui = NodePath("LossUI")
tile_query_system = NodePath("Systems/TileQuerySystem")
player_service = NodePath("Services/PlayerService")

[node name="Services" type="Node" parent="."]

[node name="PlayerService" type="Node" parent="Services"]
script = ExtResource("24_s7x28")
metadata/_custom_type_script = "uid://cndyjg0c5r3k4"

[node name="Systems" type="Node" parent="."]

[node name="LevelOutcomeSystem" parent="Systems" node_paths=PackedStringArray("level_state_component", "win_condition_systems", "loss_condition_systems") instance=ExtResource("15_5lobc")]
level_state_component = NodePath("LevelStateComponent")
win_condition_systems = [NodePath("WinConditions/PaintAllTilesConditionSystem")]
loss_condition_systems = [NodePath("LossConditions/AllPlayersDeadConditionSystem")]

[node name="LevelStateComponent" parent="Systems/LevelOutcomeSystem" instance=ExtResource("10_k5trb")]
data = SubResource("Resource_ascnc")

[node name="WinConditions" type="Node" parent="Systems/LevelOutcomeSystem"]

[node name="PaintAllTilesConditionSystem" type="Node" parent="Systems/LevelOutcomeSystem/WinConditions" node_paths=PackedStringArray("level_state_component")]
script = ExtResource("7_gj0ym")
level_state_component = NodePath("../../LevelStateComponent")

[node name="LossConditions" type="Node" parent="Systems/LevelOutcomeSystem"]

[node name="AllPlayersDeadConditionSystem" type="Node" parent="Systems/LevelOutcomeSystem/LossConditions" node_paths=PackedStringArray("health_system", "player_service")]
script = ExtResource("8_5yot2")
health_system = NodePath("../../../HealthSystem")
player_service = NodePath("../../../../Services/PlayerService")

[node name="HealthSystem" parent="Systems" instance=ExtResource("8_n4rap")]

[node name="DamageSystem" parent="Systems" node_paths=PackedStringArray("health_system") instance=ExtResource("11_iv48k")]
health_system = NodePath("../HealthSystem")

[node name="PaintSystem" parent="Systems" node_paths=PackedStringArray("level_node", "tile_query_system", "level_progress_system", "player_service") instance=ExtResource("16_paint_system")]
paint_refill_scene = ExtResource("18_paint_refill_drop")
level_node = NodePath("../..")
tile_query_system = NodePath("../TileQuerySystem")
level_progress_system = NodePath("../LevelProgressSystem")
player_service = NodePath("../../Services/PlayerService")

[node name="LevelProgressSystem" parent="Systems" node_paths=PackedStringArray("level_state_component", "level", "player_service") instance=ExtResource("19_level_progress_system")]
level_state_component = NodePath("../LevelOutcomeSystem/LevelStateComponent")
level = NodePath("../..")
min_spawn_distance_from_player = 200.0
player_service = NodePath("../../Services/PlayerService")
exit_portal_scene = ExtResource("7_portal")

[node name="TimeDilationSystem" parent="Systems" node_paths=PackedStringArray("tile_query_system", "player_service") instance=ExtResource("16_ww2lt")]
tile_query_system = NodePath("../TileQuerySystem")
player_service = NodePath("../../Services/PlayerService")

[node name="TileQuerySystem" parent="Systems" instance=ExtResource("23_ww2lt")]

[node name="MovementSystem" parent="Systems" node_paths=PackedStringArray("player_service", "tile_query_system") instance=ExtResource("18_cwwql")]
player_service = NodePath("../../Services/PlayerService")
tile_query_system = NodePath("../TileQuerySystem")

[node name="PlayerInputSystem" parent="Systems" node_paths=PackedStringArray("movement_system", "player_service") instance=ExtResource("19_kd75s")]
movement_system = NodePath("../MovementSystem")
player_service = NodePath("../../Services/PlayerService")

[node name="WallPhaseSystem" parent="Systems" node_paths=PackedStringArray("tile_query_system", "movement_system", "player_service") instance=ExtResource("24_wallphase")]
tile_query_system = NodePath("../TileQuerySystem")
movement_system = NodePath("../MovementSystem")
player_service = NodePath("../../Services/PlayerService")

[node name="WaterSystem" parent="Systems" node_paths=PackedStringArray("tile_query_system", "player_service") instance=ExtResource("19_cwwql")]
tile_query_system = NodePath("../TileQuerySystem")
player_service = NodePath("../../Services/PlayerService")

[node name="InvertedControlsSystem" parent="Systems" node_paths=PackedStringArray("player_service") instance=ExtResource("25_inverted_controls")]
player_service = NodePath("../../Services/PlayerService")

[node name="TileMapLayer" type="TileMapLayer" parent="."]
texture_filter = 1
position = Vector2(0, 22.5)
tile_map_data = PackedByteArray("AAAAAAgAAQACAAAAAAAAAAkAAQACAAAAAAAAAAoAAQACAAAAAAAAAAsAAQACAAAAAAAAAAwAAQACAAAAAAAAAA0AAQACAAAAAAAAAA4AAQACAAAAAAAAAA8AAQACAAAAAAAAABAAAQACAAAAAAAAABEAAQACAAAAAAAAABIAAQACAAAAAAAAABMAAQACAAAAAAAAABQAAQACAAAAAAAAABUAAQACAAAAAAABAAgAAAAAAAAAAQABAAkAAAAAAAAAAQABAAoAAAAAAAAAAQABAAsAAAAAAAAAAQABAAwAAAAAAAAAAQABAA0AAQACAAAAAAABAA4AAQACAAAAAAABAA8AAAAAAAAAAQABABAAAAAAAAAAAQABABEAAAAAAAAAAQABABIAAAAAAAAAAQABABMAAAAAAAAAAQABABQAAAAAAAAAAQABABUAAQACAAAAAAACAAgAAAAAAAAAAQACAAkAAAAAAAAAAQACAAoAAAAAAAAAAQACAAsAAAAAAAAAAQACAAwAAAAAAAAAAQACAA0AAAAAAAAAAQACAA4AAAAAAAAAAQACAA8AAAAAAAAAAQACABAAAAAAAAAAAQACABEAAAAAAAAAAQACABIAAAAAAAAAAQACABMAAAAAAAAAAQACABQAAAAAAAAAAQACABUAAQACAAAAAAADAAgAAAAAAAAAAQADAAkAAAAAAAAAAQADAAoAAAAAAAAAAQADAAsAAAAAAAAAAQADAAwAAAAAAAAAAQADAA0AAAAAAAAAAQADAA4AAAAAAAAAAQADAA8AAAAAAAAAAQADABAAAAAAAAAAAQADABEAAAAAAAAAAQADABIAAAAAAAAAAQADABMAAAAAAAAAAQADABQAAAAAAAAAAQADABUAAQACAAAAAAAEAAgAAAAAAAAAAQAEAAkAAAAAAAAAAQAEAAoAAQACAAAAAAAEAAsAAQACAAAAAAAEAAwAAAAAAAAAAQAEAA0AAAAAAAAAAQAEAA4AAAAAAAAAAQAEAA8AAAAAAAAAAQAEABAAAQACAAAAAAAEABEAAQACAAAAAAAEABIAAAAAAAAAAQAEABMAAAAAAAAAAQAEABQAAAAAAAAAAQAEABUAAQACAAAAAAAFAAgAAAAAAAAAAQAFAAkAAAAAAAAAAQAFAAoAAQACAAAAAAAFAAsAAQACAAAAAAAFAAwAAAAAAAAAAQAFAA0AAAAAAAAAAQAFAA4AAAAAAAAAAQAFAA8AAAAAAAAAAQAFABAAAQACAAAAAAAFABEAAQACAAAAAAAFABIAAAAAAAAAAQAFABMAAAAAAAAAAQAFABQAAAAAAAAAAQAFABUAAQACAAAAAAAGAAgAAAAAAAAAAQAGAAkAAAAAAAAAAQAGAAoAAAAAAAAAAQAGAAsAAAAAAAAAAQAGAAwAAAAAAAAAAgAGAA0AAAAAAAAAAgAGAA4AAAAAAAAAAgAGAA8AAAAAAAAAAgAGABAAAAAAAAAAAQAGABEAAAAAAAAAAQAGABIAAAAAAAAAAQAGABMAAAAAAAAAAQAGABQAAAAAAAAAAQAGABUAAQACAAAAAAAHAAgAAQAAAAEAAAAHAAkAAQACAAEAAAAHAAoAAAAAAAAAAQAHAAsAAAAAAAAAAQAHAAwAAAAAAAAAAgAHAA0AAAAAAAAAAgAHAA4AAAAAAAAAAgAHAA8AAAAAAAAAAgAHABAAAAAAAAAAAQAHABEAAAAAAAAAAQAHABIAAQAAAAEAAAAHABMAAQACAAEAAAAHABQAAAAAAAAAAQAHABUAAQACAAAAAAAIAAgAAQABAAEAAAAIAAkAAQADAAEAAAAIAAoAAAAAAAAAAQAIAAsAAAAAAAAAAQAIAAwAAAAAAAAAAgAIAA0AAAAAAAAAAgAIAA4AAAAAAAAAAgAIAA8AAAAAAAAAAgAIABAAAAAAAAAAAQAIABEAAAAAAAAAAQAIABIAAQABAAEAAAAIABMAAQADAAEAAAAIABQAAAAAAAAAAQAIABUAAQACAAAAAAAJAAgAAQABAAEAAAAJAAkAAQADAAEAAAAJAAoAAAAAAAAAAQAJAAsAAAAAAAAAAQAJAAwAAAAAAAAAAgAJAA0AAAAAAAAAAgAJAA4AAAAAAAAAAgAJAA8AAAAAAAAAAgAJABAAAAAAAAAAAQAJABEAAAAAAAAAAQAJABIAAQABAAEAAAAJABMAAQADAAEAAAAJABQAAAAAAAAAAQAJABUAAQACAAAAAAAKAAgAAAAAAAAAAQAKAAkAAAAAAAAAAQAKAAoAAAAAAAAAAQAKAAsAAAAAAAAAAQAKAAwAAAAAAAAAAgAKAA0AAAAAAAAAAgAKAA4AAAAAAAAAAgAKAA8AAAAAAAAAAgAKABAAAAAAAAAAAQAKABEAAAAAAAAAAQAKABIAAAAAAAAAAQAKABMAAAAAAAAAAQAKABQAAAAAAAAAAQAKABUAAQACAAAAAAALAAgAAAAAAAAAAQALAAkAAAAAAAAAAQALAAoAAQACAAAAAAALAAsAAQACAAAAAAALAAwAAAAAAAAAAQALAA0AAAAAAAAAAQALAA4AAAAAAAAAAQALAA8AAAAAAAAAAQALABAAAQACAAAAAAALABEAAQACAAAAAAALABIAAAAAAAAAAQALABMAAAAAAAAAAQALABQAAAAAAAAAAQALABUAAQACAAAAAAAMAAgAAAAAAAAAAQAMAAkAAAAAAAAAAQAMAAoAAQACAAAAAAAMAAsAAQACAAAAAAAMAAwAAAAAAAAAAQAMAA0AAAAAAAAAAQAMAA4AAAAAAAAAAQAMAA8AAAAAAAAAAQAMABAAAQACAAAAAAAMABQAAAAAAAAAAQAMABUAAQACAAAAAAANAAgAAAAAAAAAAQANAAkAAAAAAAAAAQANAAoAAAAAAAAAAQANAAsAAAAAAAAAAQANAAwAAAAAAAAAAQANAA0AAAAAAAAAAQANAA4AAAAAAAAAAQANAA8AAAAAAAAAAQANABAAAAAAAAAAAQANABEAAAAAAAAAAQANABIAAAAAAAAAAQANABMAAAAAAAAAAQANABQAAAAAAAAAAQANABUAAQACAAAAAAAOAAgAAAAAAAAAAQAOAAkAAAAAAAAAAQAOAAoAAAAAAAAAAQAOAAsAAAAAAAAAAQAOAAwAAAAAAAAAAQAOAA0AAAAAAAAAAQAOAA4AAAAAAAAAAQAOAA8AAAAAAAAAAQAOABAAAAAAAAAAAQAOABIAAAAAAAAAAQAOABMAAAAAAAAAAQAOABQAAAAAAAAAAQAOABUAAQACAAAAAAAPAAgAAAAAAAAAAQAPAAkAAAAAAAAAAQAPAAoAAAAAAAAAAQAPAAsAAAAAAAAAAQAPAAwAAAAAAAAAAQAPAA0AAQACAAAAAAAPAA4AAQACAAAAAAAPAA8AAAAAAAAAAQAPABAAAAAAAAAAAQAPABEAAAAAAAAAAQAPABIAAAAAAAAAAQAPABMAAAAAAAAAAQAPABQAAAAAAAAAAQAPABUAAQACAAAAAAAQAAgAAQACAAAAAAAQAAkAAQACAAAAAAAQAAoAAQACAAAAAAAQAAsAAQACAAAAAAAQAAwAAQACAAAAAAAQAA0AAQACAAAAAAAQAA4AAQACAAAAAAAQAA8AAQACAAAAAAAQABAAAQACAAAAAAAQABEAAQACAAAAAAAQABIAAQACAAAAAAAQABMAAQACAAAAAAAQABQAAQACAAAAAAAQABUAAQACAAAAAAAAAAYAAQACAAAAAAAAAAcAAQACAAAAAAABAAYAAQACAAAAAAABAAcAAAAAAAAAAQACAAYAAQACAAAAAAACAAcAAAAAAAAAAQADAAYAAQACAAAAAAADAAcAAAAAAAAAAQAEAAYAAQACAAAAAAAEAAcAAAAAAAAAAQAFAAYAAQACAAAAAAAFAAcAAAAAAAAAAQAGAAYAAQACAAAAAAAGAAcAAAAAAAAAAQAHAAYAAQACAAAAAAAHAAcAAAAAAAAAAQAIAAYAAQACAAAAAAAIAAcAAAAAAAAAAQAJAAYAAQACAAAAAAAJAAcAAAAAAAAAAQAKAAYAAQACAAAAAAAKAAcAAAAAAAAAAQALAAYAAQACAAAAAAALAAcAAAAAAAAAAQAMAAYAAQACAAAAAAAMAAcAAAAAAAAAAQANAAYAAQACAAAAAAANAAcAAAAAAAAAAQAOAAYAAQACAAAAAAAOAAcAAAAAAAAAAQAPAAYAAQACAAAAAAAPAAcAAAAAAAAAAQAQAAYAAQACAAAAAAAQAAcAAQACAAAAAAAOABEAAAAAAAAAAQAMABMAAAAAAAAAAQAMABIAAAAAAAAAAQAMABEAAQACAAAAAAA=")
tile_set = SubResource("TileSet_rccf1")

[node name="PaintBar" parent="." node_paths=PackedStringArray("player_service") instance=ExtResource("4_p6v4a")]
position = Vector2(70.375, 206.25)
player_service = NodePath("../Services/PlayerService")

[node name="ProgressBar" parent="." node_paths=PackedStringArray("level_state_component") instance=ExtResource("5_5suky")]
position = Vector2(8.5, 54.5)
level_state_component = NodePath("../Systems/LevelOutcomeSystem/LevelStateComponent")

[node name="LossUI" parent="." instance=ExtResource("10_5suky")]
visible = false

[node name="DevTools" parent="." instance=ExtResource("32_bgdgy")]
position = Vector2(11.25, 19.125)
scale = Vector2(0.29, 0.29)

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Virtual Joystick" parent="CanvasLayer" instance=ExtResource("33_s7x28")]
anchors_preset = 15
anchor_top = 0.0
anchor_right = 1.0
offset_top = 0.0
offset_right = 0.0
offset_bottom = 0.0
grow_horizontal = 2
grow_vertical = 2
deadzone_size = 40.0
joystick_mode = 2
visibility_mode = 3
action_left = "move_left"
action_right = "move_right"
action_up = "move_up"
action_down = "move_down"

[connection signal="win_condition_met" from="Systems/LevelOutcomeSystem" to="Systems/LevelProgressSystem" method="on_win_condition_met"]

[editable path="CanvasLayer/Virtual Joystick"]
</file>

<file path="levels/level_1/level_1_1.tscn">
[gd_scene load_steps=8 format=4 uid="uid://ddu485x3w8hll"]

[ext_resource type="PackedScene" uid="uid://bk5rydqrj5etw" path="res://src/level/level.tscn" id="1_clv7r"]
[ext_resource type="PackedScene" uid="uid://2o2nqedmdng0" path="res://src/color_tile/color_tile.tscn" id="2_is5tt"]
[ext_resource type="PackedScene" uid="uid://dfy35tmjoeoht" path="res://src/water_tile/water_tile.tscn" id="3_p7htr"]
[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="4_y8pj7"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_fhm7n"]
scenes/1/scene = ExtResource("2_is5tt")
scenes/2/scene = ExtResource("3_p7htr")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_tibem"]
texture = ExtResource("4_y8pj7")
texture_region_size = Vector2i(8, 8)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:0/0 = 0
0:1/0 = 0
0:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
1:1/0 = 0
1:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
2:1/0 = 0
2:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:1/0 = 0
3:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)

[sub_resource type="TileSet" id="TileSet_rfco5"]
tile_size = Vector2i(8, 8)
physics_layer_0/collision_layer = 1
physics_layer_1/collision_layer = 2
physics_layer_1/collision_mask = 0
sources/1 = SubResource("TileSetAtlasSource_tibem")
sources/0 = SubResource("TileSetScenesCollectionSource_fhm7n")

[node name="Level1-1" instance=ExtResource("1_clv7r")]
level_id = "1-1"

[node name="TileMapLayer" parent="." index="2"]
tile_map_data = PackedByteArray("AAAAAAgAAQACAAAAAAAAAAkAAQACAAAAAAAAAAoAAQACAAAAAAAAAAsAAQACAAAAAAAAAAwAAQACAAAAAAAAAA0AAQACAAAAAAAAAA4AAQACAAAAAAAAAA8AAQACAAAAAAAAABAAAQACAAAAAAAAABEAAQACAAAAAAAAABIAAQACAAAAAAAAABMAAQACAAAAAAAAABQAAQACAAAAAAAAABUAAQACAAAAAAABAAgAAAAAAAAAAQABAAkAAAAAAAAAAQABAAoAAAAAAAAAAQABAAsAAAAAAAAAAQABAAwAAAAAAAAAAQABAA0AAAAAAAAAAQABAA4AAAAAAAAAAQABAA8AAAAAAAAAAQABABAAAAAAAAAAAQABABEAAAAAAAAAAQABABIAAAAAAAAAAQABABMAAAAAAAAAAQABABQAAAAAAAAAAQABABUAAQACAAAAAAACAAgAAAAAAAAAAQACAAkAAAAAAAAAAQACAAoAAAAAAAAAAQACAAsAAAAAAAAAAQACAAwAAAAAAAAAAQACAA0AAAAAAAAAAQACAA4AAAAAAAAAAQACAA8AAAAAAAAAAQACABAAAAAAAAAAAQACABEAAAAAAAAAAQACABIAAAAAAAAAAQACABMAAAAAAAAAAQACABQAAAAAAAAAAQACABUAAQACAAAAAAADAAgAAAAAAAAAAQADAAkAAAAAAAAAAQADAAoAAAAAAAAAAQADAAsAAAAAAAAAAQADAAwAAAAAAAAAAQADAA0AAAAAAAAAAQADAA4AAAAAAAAAAQADAA8AAAAAAAAAAQADABAAAAAAAAAAAQADABEAAAAAAAAAAQADABIAAAAAAAAAAQADABMAAAAAAAAAAQADABQAAAAAAAAAAQADABUAAQACAAAAAAAEAAgAAAAAAAAAAQAEAAkAAAAAAAAAAQAEAAoAAAAAAAAAAQAEAAsAAAAAAAAAAQAEAAwAAAAAAAAAAQAEAA0AAAAAAAAAAQAEAA4AAAAAAAAAAQAEAA8AAAAAAAAAAQAEABAAAAAAAAAAAQAEABEAAAAAAAAAAQAEABIAAAAAAAAAAQAEABMAAAAAAAAAAQAEABQAAAAAAAAAAQAEABUAAQACAAAAAAAFAAgAAAAAAAAAAQAFAAkAAAAAAAAAAQAFAAoAAAAAAAAAAQAFAAsAAAAAAAAAAQAFAAwAAAAAAAAAAQAFAA0AAAAAAAAAAQAFAA4AAAAAAAAAAQAFAA8AAAAAAAAAAQAFABAAAAAAAAAAAQAFABEAAAAAAAAAAQAFABIAAAAAAAAAAQAFABMAAAAAAAAAAQAFABQAAAAAAAAAAQAFABUAAQACAAAAAAAGAAgAAAAAAAAAAQAGAAkAAAAAAAAAAQAGAAoAAAAAAAAAAQAGAAsAAAAAAAAAAQAGAAwAAQAAAAEAAAAGAA0AAQACAAEAAAAGAA4AAQACAAEAAAAGAA8AAQACAAEAAAAGABAAAQACAAEAAAAGABEAAAAAAAAAAQAGABIAAAAAAAAAAQAGABMAAAAAAAAAAQAGABQAAAAAAAAAAQAGABUAAQACAAAAAAAHAAgAAAAAAAAAAQAHAAkAAAAAAAAAAQAHAAoAAAAAAAAAAQAHAAsAAAAAAAAAAQAHAAwAAQABAAEAAAAHAA0AAQADAAEAAAAHAA4AAQADAAEAAAAHAA8AAQADAAEAAAAHABAAAQADAAEAAAAHABEAAAAAAAAAAQAHABIAAAAAAAAAAQAHABMAAAAAAAAAAQAHABQAAAAAAAAAAQAHABUAAQACAAAAAAAIAAgAAAAAAAAAAQAIAAkAAAAAAAAAAQAIAAoAAAAAAAAAAQAIAAsAAAAAAAAAAQAIAAwAAQABAAEAAAAIAA0AAQADAAEAAAAIAA4AAQADAAEAAAAIAA8AAQADAAEAAAAIABAAAQADAAEAAAAIABEAAAAAAAAAAQAIABIAAAAAAAAAAQAIABMAAAAAAAAAAQAIABQAAAAAAAAAAQAIABUAAQACAAAAAAAJAAgAAAAAAAAAAQAJAAkAAAAAAAAAAQAJAAoAAAAAAAAAAQAJAAsAAAAAAAAAAQAJAAwAAQABAAEAAAAJAA0AAQADAAEAAAAJAA4AAQADAAEAAAAJAA8AAQADAAEAAAAJABAAAQADAAEAAAAJABEAAAAAAAAAAQAJABIAAAAAAAAAAQAJABMAAAAAAAAAAQAJABQAAAAAAAAAAQAJABUAAQACAAAAAAAKAAgAAAAAAAAAAQAKAAkAAAAAAAAAAQAKAAoAAAAAAAAAAQAKAAsAAAAAAAAAAQAKAAwAAAAAAAAAAQAKAA0AAAAAAAAAAQAKAA4AAAAAAAAAAQAKAA8AAAAAAAAAAQAKABAAAAAAAAAAAQAKABEAAAAAAAAAAQAKABIAAAAAAAAAAQAKABMAAAAAAAAAAQAKABQAAAAAAAAAAQAKABUAAQACAAAAAAALAAgAAAAAAAAAAQALAAkAAAAAAAAAAQALAAoAAAAAAAAAAQALAAsAAAAAAAAAAQALAAwAAAAAAAAAAQALAA0AAAAAAAAAAQALAA4AAAAAAAAAAQALAA8AAAAAAAAAAQALABAAAAAAAAAAAQALABEAAAAAAAAAAQALABIAAAAAAAAAAQALABMAAAAAAAAAAQALABQAAAAAAAAAAQALABUAAQACAAAAAAAMAAgAAAAAAAAAAQAMAAkAAAAAAAAAAQAMAAoAAAAAAAAAAQAMAAsAAAAAAAAAAQAMAAwAAAAAAAAAAQAMAA0AAAAAAAAAAQAMAA4AAAAAAAAAAQAMAA8AAAAAAAAAAQAMABAAAAAAAAAAAQAMABQAAAAAAAAAAQAMABUAAQACAAAAAAANAAgAAAAAAAAAAQANAAkAAAAAAAAAAQANAAoAAAAAAAAAAQANAAsAAAAAAAAAAQANAAwAAAAAAAAAAQANAA0AAAAAAAAAAQANAA4AAAAAAAAAAQANAA8AAAAAAAAAAQANABAAAAAAAAAAAQANABEAAAAAAAAAAQANABIAAAAAAAAAAQANABMAAAAAAAAAAQANABQAAAAAAAAAAQANABUAAQACAAAAAAAOAAgAAAAAAAAAAQAOAAkAAAAAAAAAAQAOAAoAAAAAAAAAAQAOAAsAAAAAAAAAAQAOAAwAAAAAAAAAAQAOAA0AAAAAAAAAAQAOAA4AAAAAAAAAAQAOAA8AAAAAAAAAAQAOABAAAAAAAAAAAQAOABIAAAAAAAAAAQAOABMAAAAAAAAAAQAOABQAAAAAAAAAAQAOABUAAQACAAAAAAAPAAgAAAAAAAAAAQAPAAkAAAAAAAAAAQAPAAoAAAAAAAAAAQAPAAsAAAAAAAAAAQAPAAwAAAAAAAAAAQAPAA0AAAAAAAAAAQAPAA4AAAAAAAAAAQAPAA8AAAAAAAAAAQAPABAAAAAAAAAAAQAPABEAAAAAAAAAAQAPABIAAAAAAAAAAQAPABMAAAAAAAAAAQAPABQAAAAAAAAAAQAPABUAAQACAAAAAAAQAAgAAQACAAAAAAAQAAkAAQACAAAAAAAQAAoAAQACAAAAAAAQAAsAAQACAAAAAAAQAAwAAQACAAAAAAAQAA0AAQACAAAAAAAQAA4AAQACAAAAAAAQAA8AAQACAAAAAAAQABAAAQACAAAAAAAQABEAAQACAAAAAAAQABIAAQACAAAAAAAQABMAAQACAAAAAAAQABQAAQACAAAAAAAQABUAAQACAAAAAAAAAAYAAQACAAAAAAAAAAcAAQACAAAAAAABAAYAAQACAAAAAAABAAcAAAAAAAAAAQACAAYAAQACAAAAAAACAAcAAAAAAAAAAQADAAYAAQACAAAAAAADAAcAAAAAAAAAAQAEAAYAAQACAAAAAAAEAAcAAAAAAAAAAQAFAAYAAQACAAAAAAAFAAcAAAAAAAAAAQAGAAYAAQACAAAAAAAGAAcAAAAAAAAAAQAHAAYAAQACAAAAAAAHAAcAAAAAAAAAAQAIAAYAAQACAAAAAAAIAAcAAAAAAAAAAQAJAAYAAQACAAAAAAAJAAcAAAAAAAAAAQAKAAYAAQACAAAAAAAKAAcAAAAAAAAAAQALAAYAAQACAAAAAAALAAcAAAAAAAAAAQAMAAYAAQACAAAAAAAMAAcAAAAAAAAAAQANAAYAAQACAAAAAAANAAcAAAAAAAAAAQAOAAYAAQACAAAAAAAOAAcAAAAAAAAAAQAPAAYAAQACAAAAAAAPAAcAAAAAAAAAAQAQAAYAAQACAAAAAAAQAAcAAQACAAAAAAAOABEAAAAAAAAAAQAMABMAAAAAAAAAAQAMABIAAAAAAAAAAQAMABEAAAAAAAAAAQA=")
tile_set = SubResource("TileSet_rfco5")

[editable path="CanvasLayer/Virtual Joystick"]
</file>

<file path="levels/level_1/level_1_2.tscn">
[gd_scene load_steps=8 format=4 uid="uid://cvxssim0a0xvw"]

[ext_resource type="PackedScene" uid="uid://bk5rydqrj5etw" path="res://src/level/level.tscn" id="1_6mlg0"]
[ext_resource type="PackedScene" uid="uid://2o2nqedmdng0" path="res://src/color_tile/color_tile.tscn" id="2_rlis5"]
[ext_resource type="PackedScene" uid="uid://dfy35tmjoeoht" path="res://src/water_tile/water_tile.tscn" id="3_ef07m"]
[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="4_uhnda"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_3b70q"]
scenes/1/scene = ExtResource("2_rlis5")
scenes/2/scene = ExtResource("3_ef07m")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_c7agy"]
texture = ExtResource("4_uhnda")
texture_region_size = Vector2i(8, 8)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:0/0 = 0
0:1/0 = 0
0:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
1:1/0 = 0
1:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
2:1/0 = 0
2:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:1/0 = 0
3:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)

[sub_resource type="TileSet" id="TileSet_88qs4"]
tile_size = Vector2i(8, 8)
physics_layer_0/collision_layer = 1
physics_layer_1/collision_layer = 2
physics_layer_1/collision_mask = 0
sources/1 = SubResource("TileSetAtlasSource_c7agy")
sources/0 = SubResource("TileSetScenesCollectionSource_3b70q")

[node name="Level1-2" instance=ExtResource("1_6mlg0")]
level_id = "1-2"

[node name="TileMapLayer" parent="." index="2"]
tile_map_data = PackedByteArray("AAAAAAgAAQACAAAAAAAAAAkAAQACAAAAAAAAAAoAAQACAAAAAAAAAAsAAQACAAAAAAAAAAwAAQACAAAAAAAAAA0AAQACAAAAAAAAAA4AAQACAAAAAAAAAA8AAQACAAAAAAAAABAAAQACAAAAAAAAABEAAQACAAAAAAAAABIAAQACAAAAAAAAABMAAQACAAAAAAAAABQAAQACAAAAAAAAABUAAQACAAAAAAABAAgAAAAAAAAAAQABAAkAAAAAAAAAAQABAAoAAAAAAAAAAQABAAsAAAAAAAAAAQABAAwAAAAAAAAAAQABAA0AAAAAAAAAAQABAA4AAAAAAAAAAQABAA8AAAAAAAAAAQABABAAAAAAAAAAAQABABEAAAAAAAAAAQABABIAAAAAAAAAAQABABMAAQACAAAAAAABABQAAQACAAAAAAABABUAAQACAAAAAAACAAgAAAAAAAAAAQACAAkAAAAAAAAAAQACAAoAAAAAAAAAAQACAAsAAAAAAAAAAQACAAwAAAAAAAAAAQACAA0AAAAAAAAAAQACAA4AAAAAAAAAAQACAA8AAAAAAAAAAQACABAAAAAAAAAAAQACABEAAAAAAAAAAQACABIAAAAAAAAAAQACABMAAQACAAAAAAACABQAAQACAAAAAAACABUAAQACAAAAAAADAAgAAAAAAAAAAQADAAkAAAAAAAAAAQADAAoAAAAAAAAAAQADAAsAAAAAAAAAAQADAAwAAAAAAAAAAQADAA0AAAAAAAAAAQADAA4AAAAAAAAAAQADAA8AAAAAAAAAAQADABAAAAAAAAAAAQADABEAAAAAAAAAAQADABIAAAAAAAAAAQADABMAAQACAAAAAAADABQAAQACAAAAAAADABUAAQACAAAAAAAEAAgAAAAAAAAAAQAEAAkAAAAAAAAAAQAEAAoAAAAAAAAAAQAEAAsAAAAAAAAAAQAEAAwAAAAAAAAAAQAEAA0AAAAAAAAAAQAEAA4AAAAAAAAAAQAEAA8AAAAAAAAAAQAEABAAAAAAAAAAAQAEABEAAAAAAAAAAQAEABIAAAAAAAAAAQAEABMAAQACAAAAAAAEABQAAQACAAAAAAAEABUAAQACAAAAAAAFAAgAAAAAAAAAAQAFAAkAAAAAAAAAAQAFAAoAAAAAAAAAAQAFAAsAAAAAAAAAAQAFAAwAAAAAAAAAAQAFAA0AAAAAAAAAAQAFAA4AAAAAAAAAAQAFAA8AAAAAAAAAAQAFABAAAAAAAAAAAQAFABEAAAAAAAAAAQAFABIAAAAAAAAAAQAFABMAAAAAAAAAAQAFABQAAAAAAAAAAQAFABUAAQACAAAAAAAGAAgAAAAAAAAAAQAGAAkAAAAAAAAAAQAGAAoAAQADAAEAAAAGAAsAAQADAAEAAAAGAAwAAAAAAAAAAQAGAA0AAAAAAAAAAQAGAA4AAAAAAAAAAQAGAA8AAAAAAAAAAQAGABAAAAAAAAAAAQAGABEAAAAAAAAAAQAGABIAAAAAAAAAAQAGABMAAAAAAAAAAQAGABQAAAAAAAAAAQAGABUAAQACAAAAAAAHAAgAAAAAAAAAAQAHAAkAAAAAAAAAAQAHAAoAAQADAAEAAAAHAAsAAQADAAEAAAAHAAwAAAAAAAAAAQAHAA0AAAAAAAAAAQAHAA4AAAAAAAAAAQAHAA8AAAAAAAAAAQAHABAAAAAAAAAAAQAHABEAAAAAAAAAAQAHABIAAAAAAAAAAQAHABMAAAAAAAAAAQAHABQAAAAAAAAAAQAHABUAAQACAAAAAAAIAAgAAAAAAAAAAQAIAAkAAAAAAAAAAQAIAAoAAQADAAEAAAAIAAsAAQADAAEAAAAIAAwAAQADAAEAAAAIAA0AAQADAAEAAAAIAA4AAAAAAAAAAQAIAA8AAAAAAAAAAQAIABAAAAAAAAAAAQAIABEAAQADAAEAAAAIABIAAQADAAEAAAAIABMAAQADAAEAAAAIABQAAQADAAEAAAAIABUAAQACAAAAAAAJAAgAAAAAAAAAAQAJAAkAAAAAAAAAAQAJAAoAAQADAAEAAAAJAAsAAQADAAEAAAAJAAwAAQADAAEAAAAJAA0AAQADAAEAAAAJAA4AAAAAAAAAAQAJAA8AAAAAAAAAAQAJABAAAAAAAAAAAQAJABEAAQADAAEAAAAJABIAAQADAAEAAAAJABMAAQADAAEAAAAJABQAAQADAAEAAAAJABUAAQACAAAAAAAKAAgAAAAAAAAAAQAKAAkAAAAAAAAAAQAKAAoAAQADAAEAAAAKAAsAAQADAAEAAAAKAAwAAAAAAAAAAQAKAA0AAAAAAAAAAQAKAA4AAAAAAAAAAQAKAA8AAAAAAAAAAQAKABAAAAAAAAAAAQAKABEAAAAAAAAAAQAKABIAAAAAAAAAAQAKABMAAAAAAAAAAQAKABQAAAAAAAAAAQAKABUAAQACAAAAAAALAAgAAAAAAAAAAQALAAkAAAAAAAAAAQALAAoAAQADAAEAAAALAAsAAQADAAEAAAALAAwAAAAAAAAAAQALAA0AAAAAAAAAAQALAA4AAAAAAAAAAQALAA8AAAAAAAAAAQALABAAAAAAAAAAAQALABEAAAAAAAAAAQALABIAAAAAAAAAAQALABMAAAAAAAAAAQALABQAAAAAAAAAAQALABUAAQACAAAAAAAMAAgAAAAAAAAAAQAMAAkAAAAAAAAAAQAMAAoAAAAAAAAAAQAMAAsAAAAAAAAAAQAMAAwAAAAAAAAAAQAMAA0AAAAAAAAAAQAMAA4AAAAAAAAAAQAMAA8AAAAAAAAAAQAMABAAAAAAAAAAAQAMABQAAAAAAAAAAQAMABUAAQACAAAAAAANAAgAAAAAAAAAAQANAAkAAAAAAAAAAQANAAoAAAAAAAAAAQANAAsAAAAAAAAAAQANAAwAAAAAAAAAAQANAA0AAAAAAAAAAQANAA4AAAAAAAAAAQANAA8AAAAAAAAAAQANABAAAAAAAAAAAQANABEAAAAAAAAAAQANABIAAAAAAAAAAQANABMAAQACAAAAAAANABQAAQACAAAAAAANABUAAQACAAAAAAAOAAgAAAAAAAAAAQAOAAkAAAAAAAAAAQAOAAoAAAAAAAAAAQAOAAsAAAAAAAAAAQAOAAwAAAAAAAAAAQAOAA0AAAAAAAAAAQAOAA4AAAAAAAAAAQAOAA8AAAAAAAAAAQAOABAAAAAAAAAAAQAOABIAAAAAAAAAAQAOABMAAQACAAAAAAAOABQAAQACAAAAAAAOABUAAQACAAAAAAAPAAgAAAAAAAAAAQAPAAkAAAAAAAAAAQAPAAoAAAAAAAAAAQAPAAsAAAAAAAAAAQAPAAwAAAAAAAAAAQAPAA0AAAAAAAAAAQAPAA4AAAAAAAAAAQAPAA8AAAAAAAAAAQAPABAAAAAAAAAAAQAPABEAAAAAAAAAAQAPABIAAAAAAAAAAQAPABMAAQACAAAAAAAPABQAAQACAAAAAAAPABUAAQACAAAAAAAQAAgAAQACAAAAAAAQAAkAAQACAAAAAAAQAAoAAQACAAAAAAAQAAsAAQACAAAAAAAQAAwAAQACAAAAAAAQAA0AAQACAAAAAAAQAA4AAQACAAAAAAAQAA8AAQACAAAAAAAQABAAAQACAAAAAAAQABEAAQACAAAAAAAQABIAAQACAAAAAAAQABMAAQACAAAAAAAQABQAAQACAAAAAAAQABUAAQACAAAAAAAAAAYAAQACAAAAAAAAAAcAAQACAAAAAAABAAYAAQACAAAAAAABAAcAAAAAAAAAAQACAAYAAQACAAAAAAACAAcAAAAAAAAAAQADAAYAAQACAAAAAAADAAcAAAAAAAAAAQAEAAYAAQACAAAAAAAEAAcAAAAAAAAAAQAFAAYAAQACAAAAAAAFAAcAAAAAAAAAAQAGAAYAAQACAAAAAAAGAAcAAAAAAAAAAQAHAAYAAQACAAAAAAAHAAcAAAAAAAAAAQAIAAYAAQACAAAAAAAIAAcAAAAAAAAAAQAJAAYAAQACAAAAAAAJAAcAAAAAAAAAAQAKAAYAAQACAAAAAAAKAAcAAAAAAAAAAQALAAYAAQACAAAAAAALAAcAAAAAAAAAAQAMAAYAAQACAAAAAAAMAAcAAAAAAAAAAQANAAYAAQACAAAAAAANAAcAAAAAAAAAAQAOAAYAAQACAAAAAAAOAAcAAAAAAAAAAQAPAAYAAQACAAAAAAAPAAcAAAAAAAAAAQAQAAYAAQACAAAAAAAQAAcAAQACAAAAAAAOABEAAAAAAAAAAQAMABMAAAAAAAAAAQAMABIAAAAAAAAAAQAMABEAAAAAAAAAAQA=")
tile_set = SubResource("TileSet_88qs4")

[editable path="CanvasLayer/Virtual Joystick"]
</file>

<file path="levels/level_1/level_1_3.tscn">
[gd_scene load_steps=2 format=3 uid="uid://bbv3tfaswcfge"]

[ext_resource type="PackedScene" uid="uid://bk5rydqrj5etw" path="res://src/level/level.tscn" id="1_2hglu"]

[node name="Level1-3" instance=ExtResource("1_2hglu")]
level_id = "1-3"

[editable path="CanvasLayer/Virtual Joystick"]
</file>

<file path="levels/level_1/level_1_4.tscn">
[gd_scene load_steps=3 format=3 uid="uid://d1sj0qg3f2fo8"]

[ext_resource type="PackedScene" uid="uid://bk5rydqrj5etw" path="res://src/level/level.tscn" id="1_o8u7i"]
[node name="Level1-4" instance=ExtResource("1_o8u7i")]
level_id = "1-4"

[editable path="CanvasLayer/Virtual Joystick"]
</file>

<file path="loss_ui/loss_ui.gd">
extends CanvasLayer

signal play_again_pressed

@export var play_again_button: Button

func _ready() -> void:
	play_again_button.pressed.connect(_on_play_again_pressed)
	visibility_changed.connect(_on_visibility_changed)
	hide()

func _on_visibility_changed() -> void:
	show()

func _on_play_again_pressed() -> void:
	play_again_pressed.emit()
</file>

<file path="loss_ui/loss_ui.gd.uid">
uid://dbptl3ox843ya
</file>

<file path="loss_ui/loss_ui.tscn">
[gd_scene load_steps=2 format=3 uid="uid://b2qufv2y580sm"]

[ext_resource type="Script" uid="uid://dbptl3ox843ya" path="res://src/loss_ui/loss_ui.gd" id="1_up40x"]

[node name="LossUI" type="CanvasLayer" node_paths=PackedStringArray("play_again_button")]
script = ExtResource("1_up40x")
play_again_button = NodePath("Control/VBoxContainer/PlayAgainButton")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="ColorRect" type="ColorRect" parent="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.5)

[node name="VBoxContainer" type="VBoxContainer" parent="Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -50.0
offset_right = 100.0
offset_bottom = 50.0
grow_horizontal = 2
grow_vertical = 2

[node name="LossLabel" type="Label" parent="Control/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 100
text = "GAME OVER"
horizontal_alignment = 1

[node name="PlayAgainButton" type="Button" parent="Control/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 70
text = "Играть снова"
</file>

<file path="main/game_orchestrator.gd">
class_name GameOrchestrator
extends Node

@export var hub_scene: PackedScene
@export var run_levels: Array[PackedScene]
@export var player_scene: PackedScene

var _scenes_container: Node
var _current_scene_instance: Node
var _current_level_index: int = -1
var _run_state_service: RunStateService

func initialize(scenes_container: Node) -> void:
	self._scenes_container = scenes_container

func start_run() -> void:
	_on_start_run()

func go_to_hub() -> void:
	_cleanup_run_state_service()
	go_to_scene(hub_scene)

func go_to_scene(scene_resource: PackedScene) -> void:
	GameStateService.set_state(GameStateService.GameState.TRANSITIONING)
	await _remove_current_scene()
	_add_new_scene(scene_resource)

func _remove_current_scene() -> void:
	if not is_instance_valid(_current_scene_instance):
		return

	_disconnect_current_scene_signals()
	_current_scene_instance.queue_free()
	await _current_scene_instance.tree_exited

func _add_new_scene(scene_resource: PackedScene) -> void:
	var new_scene: Node = scene_resource.instantiate()
	_scenes_container.add_child(new_scene)
	_current_scene_instance = new_scene

	if _current_scene_instance is BaseLevel:
		var base_level: BaseLevel = _current_scene_instance as BaseLevel
		base_level.ready_for_spawn.connect(_on_scene_ready_for_spawn.bind(base_level), CONNECT_ONE_SHOT)

	_connect_current_scene_signals()

func _on_scene_ready_for_spawn(scene: BaseLevel) -> void:
	scene.prepare_scene()
	scene.setup_and_spawn_player(player_scene)

	StatService.initialize_for_run(scene.player_service.get_stats_component())
	_initialize_player_components(scene.player_service)

	if scene is Level:
		GameStateService.set_state(GameStateService.GameState.IN_LEVEL)
	elif scene is Hub:
		GameStateService.set_state(GameStateService.GameState.HUB)

func _connect_current_scene_signals() -> void:
	if _current_scene_instance is Level:
		var level_instance: Level = _current_scene_instance as Level
		level_instance.level_completed.connect(_on_level_completed)
		level_instance.level_failed.connect(_on_level_failed)
	elif _current_scene_instance is Hub:
		var hub_instance: Hub = _current_scene_instance as Hub
		hub_instance.start_run.connect(_on_start_run)

func _disconnect_current_scene_signals() -> void:
	if _current_scene_instance is Level:
		var level_instance: Level = _current_scene_instance as Level
		level_instance.level_completed.disconnect(_on_level_completed)
		level_instance.level_failed.disconnect(_on_level_failed)
	elif _current_scene_instance is Hub:
		var hub_instance: Hub = _current_scene_instance as Hub
		hub_instance.start_run.disconnect(_on_start_run)

func _on_start_run() -> void:
	_current_level_index = 0
	_create_run_state_service()
	if not run_levels.is_empty():
		go_to_scene(run_levels[_current_level_index])
	else:
		push_error("Нет уровней для запуска!")
		go_to_hub()

func _on_level_completed() -> void:
	_collect_level_data()
	_current_level_index += 1
	if _current_level_index < run_levels.size():
		go_to_scene(run_levels[_current_level_index])
	else:
		_convert_run_currency()
		go_to_hub()

func _on_level_failed() -> void:
	if StatService.spend_extra_life():
		go_to_scene(run_levels[_current_level_index])
	else:
		go_to_hub()

func _create_run_state_service() -> void:
	_run_state_service = RunStateService.new()
	add_child(_run_state_service)
	_run_state_service.reset()

func _collect_level_data() -> void:
	if _current_scene_instance is Level:
		var level_instance: Level = _current_scene_instance as Level
		var level_state_component: LevelStateComponent = level_instance.level_state_component
		var painted_tiles: int = level_state_component.data.painted_tiles
		_run_state_service.add_painted_tiles(painted_tiles)

func _convert_run_currency() -> void:
	var total_painted_tiles: int = _run_state_service.get_total_painted_tiles()
	if total_painted_tiles > 0:
		GameProgress.add_meta_currency(total_painted_tiles)

func _initialize_player_components(player_service: PlayerService) -> void:
	var health_component: HealthComponent = player_service.get_health_component()
	if is_instance_valid(health_component):
		health_component.initialize()

	var paint_component: PaintComponent = player_service.get_paint_component()
	if is_instance_valid(paint_component):
		paint_component.initialize()

func _cleanup_run_state_service() -> void:
	_run_state_service.queue_free()
	_run_state_service = null
</file>

<file path="main/game_orchestrator.gd.uid">
uid://dfpgio0c1duyy
</file>

<file path="main/main.gd">
extends Node

@export var initial_scene: PackedScene
@export var orchestrator: GameOrchestrator
@export var scene_container: Node

func _ready() -> void:
    orchestrator.initialize(scene_container)

    if initial_scene:
        orchestrator.go_to_scene(initial_scene)
</file>

<file path="main/main.gd.uid">
uid://6bq5j2lpieqg
</file>

<file path="main/main.tscn">
[gd_scene load_steps=9 format=3 uid="uid://cepew8eycqnwb"]

[ext_resource type="Script" uid="uid://6bq5j2lpieqg" path="res://src/main/main.gd" id="1_3s6yk"]
[ext_resource type="PackedScene" uid="uid://bqx8h4n5m2k7p" path="res://src/hub/hub.tscn" id="2_ab7mr"]
[ext_resource type="Script" uid="uid://dfpgio0c1duyy" path="res://src/main/game_orchestrator.gd" id="2_orchestrator"]
[ext_resource type="PackedScene" uid="uid://ddu485x3w8hll" path="res://src/levels/level_1/level_1_1.tscn" id="4_30cu5"]
[ext_resource type="PackedScene" uid="uid://cvxssim0a0xvw" path="res://src/levels/level_1/level_1_2.tscn" id="5_frqgp"]
[ext_resource type="PackedScene" uid="uid://bbv3tfaswcfge" path="res://src/levels/level_1/level_1_3.tscn" id="6_2nke2"]
[ext_resource type="PackedScene" uid="uid://d1sj0qg3f2fo8" path="res://src/levels/level_1/level_1_4.tscn" id="7_km7bn"]
[ext_resource type="PackedScene" uid="uid://cxhc4tig2eklx" path="res://src/player/characters/apple.tscn" id="8_apple"]

[node name="Main" type="Node" node_paths=PackedStringArray("orchestrator", "scene_container")]
script = ExtResource("1_3s6yk")
initial_scene = ExtResource("2_ab7mr")
orchestrator = NodePath("GameOrchestrator")
scene_container = NodePath("SceneContainer")

[node name="GameOrchestrator" type="Node" parent="."]
script = ExtResource("2_orchestrator")
hub_scene = ExtResource("2_ab7mr")
run_levels = Array[PackedScene]([ExtResource("4_30cu5"), ExtResource("5_frqgp"), ExtResource("6_2nke2"), ExtResource("7_km7bn")])
player_scene = ExtResource("8_apple")

[node name="SceneContainer" type="Node" parent="."]
</file>

<file path="movement/movement_component.gd">
class_name MovementComponent
extends Node

signal movement_started(direction: Vector2)
signal movement_completed()
signal movement_blocked(direction: Vector2)

@export var data: MovementData
@export var actor: CharacterBody2D
@export var collision_ray: RayCast2D

var is_moving: bool = false
var start_position: Vector2 = Vector2.ZERO
var target_position: Vector2 = Vector2.ZERO
var progress: float = 0.0

func _ready() -> void:
    add_to_group("movement_components")
</file>

<file path="movement/movement_component.gd.uid">
uid://bpmyflh277fbw
</file>

<file path="movement/movement_component.tscn">
[gd_scene load_steps=4 format=3 uid="uid://k2v1icx8llrc"]

[ext_resource type="Script" uid="uid://bpmyflh277fbw" path="res://src/movement/movement_component.gd" id="1_ixeon"]
[ext_resource type="Script" uid="uid://c1gtewldstgdg" path="res://src/movement/movement_data.gd" id="2_1ryje"]

[sub_resource type="Resource" id="Resource_dchgf"]
script = ExtResource("2_1ryje")
tile_size = 8
move_duration = 0.2
metadata/_custom_type_script = "uid://c1gtewldstgdg"

[node name="MovementComponent" type="Node" node_paths=PackedStringArray("collision_ray")]
script = ExtResource("1_ixeon")
data = SubResource("Resource_dchgf")
collision_ray = NodePath("")
</file>

<file path="movement/movement_data.gd">
class_name MovementData
extends Resource

@export var tile_size: int
@export var move_duration: float
</file>

<file path="movement/movement_data.gd.uid">
uid://c1gtewldstgdg
</file>

<file path="movement/movement_system.gd">
class_name MovementSystem
extends Node

@export var player_service: PlayerService
@export var tile_query_system: TileQuerySystem

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	_snap_player_to_grid.call_deferred()

func _process(delta: float) -> void:
	var components: Array[Node] = get_tree().get_nodes_in_group(&"movement_components")
	for node: Node in components:
		var component: MovementComponent = node as MovementComponent
		if component == null or component.data == null or component.actor == null:
			continue

		if not component.is_moving:
			continue

		var move_duration: float = StatService.get_move_duration()
		var increment: float = delta / max(0.001, move_duration)
		component.progress = move_toward(component.progress, 1.0, increment)
		component.actor.position = component.start_position.lerp(component.target_position, component.progress)

		if is_equal_approx(component.progress, 1.0):
			component.actor.position = component.target_position
			component.is_moving = false
			component.progress = 0.0
			component.movement_completed.emit()

func move(component: MovementComponent, direction: Vector2) -> void:
	if component == null or component.data == null or component.actor == null or component.collision_ray == null:
		return

	if component.is_moving:
		return

	var target_pos: Vector2 = direction * StatService.get_tile_size()
	component.collision_ray.target_position = target_pos
	component.collision_ray.force_raycast_update()

	if component.collision_ray.is_colliding():
		component.movement_blocked.emit(direction)
		return

	component.start_position = component.actor.position
	component.target_position = component.actor.position + target_pos
	component.is_moving = true
	component.progress = 0.0
	component.movement_started.emit(direction)

func teleport(component: MovementComponent, global_target_pos: Vector2) -> void:
	if component == null or component.data == null or component.actor == null:
		return

	if component.is_moving:
		return

	if component.collision_ray != null:
		var saved_mask: int = component.collision_ray.collision_mask
		component.collision_ray.collision_mask = 0

		component.movement_completed.connect(
			func() -> void:
				if is_instance_valid(component) and is_instance_valid(component.collision_ray):
					component.collision_ray.collision_mask = saved_mask,
					CONNECT_ONE_SHOT
		)

	var local_target_pos: Vector2 = (component.actor.get_parent() as Node2D).to_local(global_target_pos)

	component.start_position = component.actor.position
	component.target_position = local_target_pos
	component.is_moving = true
	component.progress = 0.0
	component.movement_started.emit(local_target_pos - component.start_position)

func _snap_player_to_grid() -> void:
	var player: Node2D = player_service.get_player_node()
	var nearest_tile: Node2D = tile_query_system.get_nearest_tile(player.global_position)
	if is_instance_valid(nearest_tile):
		player.global_position = nearest_tile.global_position
</file>

<file path="movement/movement_system.gd.uid">
uid://cphudnfr3et03
</file>

<file path="movement/movement_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://bi0dhs7jf7yrl"]

[ext_resource type="Script" uid="uid://cphudnfr3et03" path="res://src/movement/movement_system.gd" id="1_8btrt"]

[node name="MovementSystem" type="Node"]
script = ExtResource("1_8btrt")
</file>

<file path="paint/paint_component.gd">
class_name PaintComponent
extends Node

signal paint_changed(current_paint: int, max_paint: int)

var max_paint: int
var current_paint: int:
	set(new_value):
		var clamped_value: int = clamp(new_value, 0, max_paint)
		if current_paint == clamped_value:
			return
		current_paint = clamped_value
		paint_changed.emit(current_paint, max_paint)

func initialize() -> void:
	max_paint = StatService.get_max_paint()
	current_paint = max_paint

func reset() -> void:
	current_paint = max_paint
</file>

<file path="paint/paint_component.gd.uid">
uid://3wwhgpeett5y
</file>

<file path="paint/paint_component.tscn">
[gd_scene load_steps=3 format=3 uid="uid://paint_component_uid"]

[ext_resource type="Script" path="res://src/paint/paint_component.gd" id="1_paint_component"]
[ext_resource type="Script" path="res://src/paint/paint_data.gd" id="2_paint_data"]

[sub_resource type="Resource" id="PaintData_resource"]
script = ExtResource("2_paint_data")
max_paint = 10
metadata/_custom_type_script = "uid://paint_data_uid"

[node name="PaintComponent" type="Node"]
script = ExtResource("1_paint_component")
data = SubResource("PaintData_resource")
</file>

<file path="paint/paint_data.gd">
class_name PaintData
extends Resource

@export var paint_color: Color = Color.WHITE
@export var max_paint: int = 10
</file>

<file path="paint/paint_data.gd.uid">
uid://s041hcolx1gq
</file>

<file path="paint/paint_system.gd">
class_name PaintSystem
extends Node

@export var paint_refill_scene: PackedScene
@export var level_node: Node2D
@export var tile_query_system: TileQuerySystem
@export var level_progress_system: LevelProgressSystem
@export var player_service: PlayerService

var _player_node: Node2D
var _player_paint_component: PaintComponent
var _player_ability_component: AbilityComponent
var _player_movement_component: MovementComponent

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node_param: Node2D) -> void:
	_player_node = player_service.get_player_node()
	_player_paint_component = player_service.get_paint_component()
	_player_ability_component = player_service.get_ability_component()
	_player_movement_component = player_service.get_movement_component()
	_player_movement_component.movement_completed.connect(_on_player_movement_completed)

func _physics_process(_delta: float) -> void:
	if not is_instance_valid(_player_paint_component):
		return

	_handle_refills()

func _try_paint_tile(paint_component: PaintComponent, tile_to_paint: ColorTile, color: Color) -> bool:
	if not _can_process_paint_request(paint_component, tile_to_paint):
		return false

	tile_to_paint.paint(color)
	_consume_paint(paint_component)
	return true

func _add_paint(paint_component: PaintComponent, amount: int) -> void:
	paint_component.current_paint += amount

func _on_player_movement_completed() -> void:
	var current_tile: Node2D = tile_query_system.get_tile_at_global_pos(_player_node.global_position)
	if not is_instance_valid(current_tile) or current_tile is not ColorTile:
		return

	var color_tile: ColorTile = current_tile as ColorTile
	var painted_successfully: bool = _try_paint_tile(_player_paint_component, color_tile, StatService.get_paint_color())

	if not painted_successfully and color_tile.is_painted():
		var ability_node: Ability = _player_ability_component.get_ability(RestorePaintAbilityData)
		if is_instance_valid(ability_node):
			var ability_data: RestorePaintAbilityData = ability_node.data as RestorePaintAbilityData
			_add_paint(_player_paint_component, ability_data.paint_to_restore)

func _can_process_paint_request(paint_component: PaintComponent, tile_to_paint: ColorTile) -> bool:
	if not is_instance_valid(tile_to_paint):
		return false
	if tile_to_paint.is_painted():
		return false
	if not _has_enough_paint(paint_component):
		return false

	return true

func _has_enough_paint(paint_component: PaintComponent) -> bool:
	return paint_component.current_paint > 0

func _consume_paint(paint_component: PaintComponent) -> void:
	paint_component.current_paint -= 1

func _handle_refills() -> void:
	var target_drop_count: int = 1
	var multi_drop_ability: Ability = _player_ability_component.get_ability(MultiDropAbilityData)
	if is_instance_valid(multi_drop_ability):
		var multi_drop_ability_data: MultiDropAbilityData = multi_drop_ability.data as MultiDropAbilityData
		target_drop_count = multi_drop_ability_data.drop_count

	var refill_nodes: Array[Node] = get_tree().get_nodes_in_group(&"paint_refills")
	var current_drop_count: int = refill_nodes.size()

	if current_drop_count < target_drop_count:
		var drops_to_spawn: int = target_drop_count - current_drop_count
		_spawn_refills(drops_to_spawn)

	for node: Node in refill_nodes:
		_process_potential_collection(node)

func _spawn_refills(count: int) -> void:
	var existing_refills: Array[Node] = get_tree().get_nodes_in_group(&"paint_refills")
	var occupied_positions: Dictionary = {}
	for refill_node: Node2D in existing_refills:
		occupied_positions[refill_node.global_position] = true

	var potential_tiles: Array[Node2D] = level_progress_system.get_safe_tiles()
	var available_tiles: Array[Node2D] = []
	for tile: Node2D in potential_tiles:
		if not occupied_positions.has(tile.global_position):
			available_tiles.append(tile)

	for i in range(count):
		if available_tiles.is_empty():
			break

		var random_tile: Node2D = available_tiles.pick_random()
		available_tiles.erase(random_tile)

		var spawn_position: Vector2 = random_tile.global_position
		var new_drop: Node2D = paint_refill_scene.instantiate() as Node2D
		level_node.add_child(new_drop)
		new_drop.global_position = spawn_position

func _process_potential_collection(refill_node: Node) -> void:
	var component: PaintRefillComponent = refill_node.find_child(&"PaintRefillComponent", true, false)
	if component == null or component.data == null:
		return

	var area: Area2D = refill_node.find_child(&"InteractionArea", true, false)
	if area == null:
		return

	var bodies: Array[Node2D] = area.get_overlapping_bodies()
	for body: Node2D in bodies:
		var paint_component: PaintComponent = body.find_child(&"PaintComponent", true, false)

		if paint_component != null:
			_add_paint(paint_component, component.data.refill_amount)
			refill_node.queue_free()
			return
</file>

<file path="paint/paint_system.gd.uid">
uid://ctjnfa0mxc8qr
</file>

<file path="paint/paint_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://cwq6ex0lmj7al"]

[ext_resource type="Script" uid="uid://ctjnfa0mxc8qr" path="res://src/paint/paint_system.gd" id="1_paint_system"]

[node name="PaintSystem" type="Node"]
script = ExtResource("1_paint_system")
</file>

<file path="paint_bar/paint_bar.gd">
class_name PaintBar
extends Node2D

@export var player_service: PlayerService
@export var drops_container: HBoxContainer
@export var frame: NinePatchRect
@export var drop_scene: PackedScene

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	var paint_component: PaintComponent = player_service.get_paint_component()
	paint_component.paint_changed.connect(_redraw)
	_redraw(paint_component.current_paint, paint_component.max_paint)

func _redraw(current: int, max_val: int) -> void:
	for child in drops_container.get_children():
		child.queue_free()

	for i in range(max_val):
		var drop_instance: Node = drop_scene.instantiate()
		drops_container.add_child(drop_instance)
		if drop_instance is CanvasItem:
			(drop_instance as CanvasItem).visible = (i < current)

	_resize_frame(max_val)


func _resize_frame(max_paint: int) -> void:
	if frame == null or max_paint <= 0:
		return

	if drops_container.get_child_count() == 0:
		return

	var drop: Control = drops_container.get_child(0) as Control
	if not is_instance_valid(drop):
		return

	var drop_w: float = drop.custom_minimum_size.x

	var sep: int = drops_container.get_theme_constant("separation")
	var mc: MarginContainer = drops_container.get_parent() as MarginContainer
	var ml: int = mc.get_theme_constant("margin_left")
	var content_w: float = max_paint * drop_w * sep + ml + 0.5

	frame.custom_minimum_size.x = content_w
	frame.size = Vector2(content_w, frame.size.y)

	var viewport: Viewport = get_viewport()
	if viewport == null:
		return

	var viewport_size: Vector2 = viewport.get_visible_rect().size
	frame.position.x = - content_w / 2.0
	global_position.x = viewport_size.x / 2.0
</file>

<file path="paint_bar/paint_bar.gd.uid">
uid://cegt7qtr3ttwp
</file>

<file path="paint_bar/paint_bar.tscn">
[gd_scene load_steps=5 format=3 uid="uid://ce5u8lunfmkij"]

[ext_resource type="Texture2D" uid="uid://croipc1prk67w" path="res://assets/paint_bar_texture.png" id="1_nbdyy"]
[ext_resource type="Texture2D" uid="uid://biagw5gvp674n" path="res://assets/color_drop.png" id="2_lvah4"]
[ext_resource type="Script" uid="uid://cegt7qtr3ttwp" path="res://src/paint_bar/paint_bar.gd" id="3_paint_bar"]
[ext_resource type="PackedScene" uid="uid://bqhqhqhqhqhqh" path="res://src/paint_bar/paint_drop.tscn" id="4_paint_drop"]

[node name="PaintBar" type="Node2D" node_paths=PackedStringArray("drops_container", "frame")]
script = ExtResource("3_paint_bar")
drops_container = NodePath("NinePatchRect/MarginContainer/HBoxContainer")
frame = NodePath("NinePatchRect")
drop_scene = ExtResource("4_paint_drop")

[node name="NinePatchRect" type="NinePatchRect" parent="."]
texture_filter = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -36.0
offset_top = -4.0
offset_right = 36.0
offset_bottom = 4.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("1_nbdyy")
region_rect = Rect2(0, 0, 8, 8)
patch_margin_left = 1
patch_margin_top = 1
patch_margin_right = 1
patch_margin_bottom = 1

[node name="MarginContainer" type="MarginContainer" parent="NinePatchRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 2
theme_override_constants/margin_top = 2
theme_override_constants/margin_right = 2
theme_override_constants/margin_bottom = 2

[node name="HBoxContainer" type="HBoxContainer" parent="NinePatchRect/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 2

[node name="ColorDrop" type="Sprite2D" parent="NinePatchRect"]
texture_filter = 1
position = Vector2(-7, 4)
texture = ExtResource("2_lvah4")
</file>

<file path="paint_bar/paint_drop.tscn">
[gd_scene format=3 uid="uid://bbyw7vvihsuep"]

[node name="PaintDrop" type="ColorRect"]
custom_minimum_size = Vector2(1.5, 0)
color = Color(1, 0.913725, 0.133333, 1)
</file>

<file path="paint_refill/paint_refill_component.gd">
class_name PaintRefillComponent
extends Node

@export var data: PaintRefillData
</file>

<file path="paint_refill/paint_refill_component.gd.uid">
uid://d1ynxqotd0fl1
</file>

<file path="paint_refill/paint_refill_component.tscn">
[gd_scene load_steps=2 format=3 uid="uid://bqxh8h8r1m5v4"]

[ext_resource type="Script" path="res://src/paint_refill/paint_refill_component.gd" id="1_abcde"]

[node name="PaintRefillComponent" type="Node"]
script = ExtResource("1_abcde")
</file>

<file path="paint_refill/paint_refill_data.gd">
class_name PaintRefillData
extends Resource

@export var refill_amount: int = 5
</file>

<file path="paint_refill/paint_refill_data.gd.uid">
uid://dvf5124sabmon
</file>

<file path="paint_refill/paint_refill_drop.tscn">
[gd_scene load_steps=6 format=3 uid="uid://se1apgxlfmx6"]

[ext_resource type="Texture2D" uid="uid://biagw5gvp674n" path="res://assets/color_drop.png" id="1_abcde"]
[ext_resource type="PackedScene" uid="uid://bqxh8h8r1m5v4" path="res://src/paint_refill/paint_refill_component.tscn" id="2_fghij"]
[ext_resource type="Script" uid="uid://dvf5124sabmon" path="res://src/paint_refill/paint_refill_data.gd" id="3_klmno"]

[sub_resource type="CircleShape2D" id="CircleShape2D_67890"]
radius = 1.4

[sub_resource type="Resource" id="Resource_12345"]
script = ExtResource("3_klmno")
refill_amount = 5

[node name="PaintRefillDrop" type="Node2D" groups=["paint_refills"]]

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
position = Vector2(0, -2.97)
texture = ExtResource("1_abcde")

[node name="InteractionArea" type="Area2D" parent="."]
position = Vector2(0, -3)
collision_layer = 0

[node name="CollisionShape2D" type="CollisionShape2D" parent="InteractionArea"]
position = Vector2(-0.48, 0.6)
shape = SubResource("CircleShape2D_67890")

[node name="PaintRefillComponent" parent="." instance=ExtResource("2_fghij")]
data = SubResource("Resource_12345")
</file>

<file path="paint_refill/paint_refill_system.gd.uid">
uid://boa32ersxu0fq
</file>

<file path="player/characters/apple.tscn">
[gd_scene load_steps=4 format=3 uid="uid://cxhc4tig2eklx"]

[ext_resource type="PackedScene" uid="uid://dq0bjiqkyl4wv" path="res://src/player/player.tscn" id="1_3jrxr"]
[ext_resource type="Resource" uid="uid://deedvv7l5unke" path="res://resources/characters/apple_stats.tres" id="2_3w74p"]
[ext_resource type="Texture2D" uid="uid://b0kc3eu271qxd" path="res://assets/apple.png" id="6_m1yns"]

[node name="Apple" instance=ExtResource("1_3jrxr")]

[node name="StatsComponent" parent="." index="0"]
base_stats = ExtResource("2_3w74p")

[node name="Sprite2D" parent="." index="6"]
texture = ExtResource("6_m1yns")

[node name="Collision" parent="." index="7"]
position = Vector2(0, -4)

[node name="Hitbox" parent="." index="8"]
position = Vector2(-1, -4)
</file>

<file path="player/characters/banana.tscn">
[gd_scene load_steps=6 format=3 uid="uid://c1mrsuk0j1dqe"]

[ext_resource type="PackedScene" uid="uid://dq0bjiqkyl4wv" path="res://src/player/player.tscn" id="1_jf61d"]
[ext_resource type="Resource" uid="uid://x444mjpwnbg8" path="res://resources/characters/banana_stats.tres" id="2_s2dbu"]
[ext_resource type="Script" uid="uid://72hm77eoq5jd" path="res://src/ability/ability_data.gd" id="4_5hrh8"]
[ext_resource type="Script" uid="uid://b5xc2p1kg3ho6" path="res://src/ability/wall_phase_ability_data.gd" id="7_fwkl5"]

[sub_resource type="Resource" id="Resource_nlfcc"]
script = ExtResource("7_fwkl5")
paint_cost = 1
metadata/_custom_type_script = "uid://b5xc2p1kg3ho6"

[node name="Banana" instance=ExtResource("1_jf61d")]

[node name="StatsComponent" parent="." index="0"]
base_stats = ExtResource("2_s2dbu")

[node name="AbilityComponent" parent="." index="3"]
ability_data_list = Array[ExtResource("4_5hrh8")]([SubResource("Resource_nlfcc")])
</file>

<file path="player/characters/kiwi.tscn">
[gd_scene load_steps=4 format=3 uid="uid://w168qw4henll"]

[ext_resource type="PackedScene" uid="uid://dq0bjiqkyl4wv" path="res://src/player/player.tscn" id="1_uu4ww"]
[ext_resource type="Resource" uid="uid://y8wx5rcbmnpb" path="res://resources/characters/kiwi_stats.tres" id="2_jcqxj"]
[ext_resource type="Texture2D" uid="uid://d33eq8cdrqadp" path="res://assets/kiwi.png" id="6_0dk4l"]

[node name="Kiwi" instance=ExtResource("1_uu4ww")]

[node name="StatsComponent" parent="." index="0"]
base_stats = ExtResource("2_jcqxj")

[node name="Sprite2D" parent="." index="6"]
texture = ExtResource("6_0dk4l")

[node name="Collision" parent="." index="7"]
position = Vector2(0.49, -4)

[node name="Hitbox" parent="." index="8"]
position = Vector2(0.49, -4)

[node name="CollisionShape2D" parent="Hitbox" index="0"]
position = Vector2(0.07, 1)
</file>

<file path="player/characters/orange.tscn">
[gd_scene load_steps=4 format=3 uid="uid://40v80kunrtu1"]

[ext_resource type="PackedScene" uid="uid://dq0bjiqkyl4wv" path="res://src/player/player.tscn" id="1_itthl"]
[ext_resource type="Resource" uid="uid://c6o5y5cbns7mi" path="res://resources/characters/orange_stats.tres" id="2_ww5h6"]
[ext_resource type="Texture2D" uid="uid://dia1elmq6p4y" path="res://assets/orange.png" id="6_o46ri"]

[node name="Orange" instance=ExtResource("1_itthl")]

[node name="StatsComponent" parent="." index="0"]
base_stats = ExtResource("2_ww5h6")

[node name="Sprite2D" parent="." index="6"]
texture = ExtResource("6_o46ri")

[node name="Collision" parent="." index="7"]
position = Vector2(0, -4)

[node name="Hitbox" parent="." index="8"]
position = Vector2(-1, -4)
</file>

<file path="player/input_component.gd">
class_name InputComponent
extends Node

@export var data: InputData
</file>

<file path="player/input_component.gd.uid">
uid://b8h4x3wexnbsw
</file>

<file path="player/input_component.tscn">
[gd_scene load_steps=2 format=3 uid="uid://bncyxtrrca85p"]

[ext_resource type="Script" uid="uid://b8h4x3wexnbsw" path="res://src/player/input_component.gd" id="1_input_component"]

[node name="InputComponent" type="Node"]
script = ExtResource("1_input_component")
</file>

<file path="player/input_data.gd">
class_name InputData
extends Resource

## Задержка перед началом повторного движения при удержании клавиши.
@export var initial_repeat_delay: float = 0.3

## Интервал между повторными движениями при удержании клавиши.
@export var repeat_rate: float = 0.2
</file>

<file path="player/input_data.gd.uid">
uid://iyhp53qcpf03
</file>

<file path="player/player_base_stats_data.gd">
class_name PlayerBaseStatsData
extends Resource

@export_group("Core Stats")
@export var max_health: int = 1
@export var max_paint: int = 10
@export var move_duration: float = 0.15
@export var extra_lives: int = 0
@export var size_modifier: float = 1.0

@export_group("Movement")
@export var tile_size: int = 8

@export_group("Input")
@export var initial_repeat_delay: float = 0.15
@export var repeat_rate: float = 0.1

@export_group("Other Properties")
@export var paint_color: Color = Color.WHITE
</file>

<file path="player/player_base_stats_data.gd.uid">
uid://jwln6lufvucu
</file>

<file path="player/player_input_system.gd">
class_name PlayerInputSystem
extends Node

@export var movement_system: MovementSystem
@export var player_service: PlayerService

var _player_movement_component: MovementComponent
var _player_ability_component: AbilityComponent
var _player_input_component: InputComponent

var _current_direction: Vector2 = Vector2.ZERO
var _hold_timer: float = 0.0
var _repeat_mode_active: bool = false
var _movement_cooldown: float = 0.0

func _ready() -> void:
	GameStateService.state_changed.connect(_on_game_state_changed)
	reset_state()
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	_player_movement_component = player_service.get_movement_component()
	_player_ability_component = player_service.get_ability_component()
	_player_input_component = player_service.get_input_component()


func _process(delta: float) -> void:
	if not GameStateService.is_gameplay_active():
		return

	if not is_instance_valid(_player_movement_component) or not is_instance_valid(movement_system) or not is_instance_valid(_player_input_component):
		return

	var input_vector: Vector2 = Input.get_vector(&"move_left", &"move_right", &"move_up", &"move_down")
	var new_direction: Vector2 = _get_dominant_direction(input_vector)

	new_direction *= _get_inversion_multiplier()

	if new_direction != _current_direction:
		_current_direction = new_direction
		_hold_timer = 0.0
		_repeat_mode_active = false
		if _current_direction != Vector2.ZERO:
			_try_move()

	if _movement_cooldown > 0.0:
		_movement_cooldown -= delta

	if _player_movement_component.is_moving:
		return

	if _current_direction != Vector2.ZERO and _movement_cooldown <= 0.0:
		_hold_timer += delta
		if not _repeat_mode_active and _hold_timer >= StatService.get_initial_repeat_delay():
			_repeat_mode_active = true
			_hold_timer = 0.0
			_try_move()
		elif _repeat_mode_active and _hold_timer >= StatService.get_repeat_rate():
			_hold_timer = 0.0
			_try_move()

func _get_dominant_direction(vector: Vector2) -> Vector2:
	if vector == Vector2.ZERO:
		return Vector2.ZERO

	if abs(vector.x) > abs(vector.y):
		var vector_sign: float = sign(vector.x)
		return Vector2(vector_sign, 0.0)
	else:
		var vector_sign: float = sign(vector.y)
		return Vector2(0.0, vector_sign)


func _try_move() -> void:
	if not _player_movement_component.is_moving and _movement_cooldown <= 0.0 and _current_direction != Vector2.ZERO:
		movement_system.move(_player_movement_component, _current_direction)
		_movement_cooldown = StatService.get_repeat_rate()


func _get_inversion_multiplier() -> float:
	if not is_instance_valid(_player_ability_component):
		return 1.0

	if _player_ability_component.has_ability(InvertedControlsAbilityData):
		return -1.0

	return 1.0

func reset_state() -> void:
	_current_direction = Vector2.ZERO
	_hold_timer = 0.0
	_repeat_mode_active = false
	_movement_cooldown = 0.0

func _on_game_state_changed(new_state: GameStateService.GameState) -> void:
	if new_state == GameStateService.GameState.IN_LEVEL or new_state == GameStateService.GameState.HUB:
		reset_state()
</file>

<file path="player/player_input_system.gd.uid">
uid://ckgkujrcsm5nq
</file>

<file path="player/player_input_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://5agsxx7ekdi6"]

[ext_resource type="Script" uid="uid://ckgkujrcsm5nq" path="res://src/player/player_input_system.gd" id="1_st4i6"]

[node name="PlayerInputSystem" type="Node"]
script = ExtResource("1_st4i6")
</file>

<file path="player/player_service.gd">
class_name PlayerService
extends Node

signal player_initialized(player_node: Node2D)

var _player_node: Node2D
var _movement_component: MovementComponent
var _paint_component: PaintComponent
var _ability_component: AbilityComponent
var _input_component: InputComponent
var _health_component: HealthComponent
var _stats_component: StatsComponent
var _hitbox: Area2D

func initialize(player_node: Node2D) -> void:
	_player_node = player_node
	if not is_instance_valid(_player_node):
		push_error("PlayerService: Provided player node is invalid.")
		return

	_movement_component = _player_node.find_child(&"MovementComponent", true, false)
	if not is_instance_valid(_movement_component):
		push_error("PlayerService: Failed to find MovementComponent.")

	_paint_component = _player_node.find_child(&"PaintComponent", true, false)
	if not is_instance_valid(_paint_component):
		push_error("PlayerService: Failed to find PaintComponent.")

	_ability_component = _player_node.find_child(&"AbilityComponent", true, false)
	if not is_instance_valid(_ability_component):
		push_error("PlayerService: Failed to find AbilityComponent.")

	_input_component = _player_node.find_child(&"InputComponent", true, false)
	if not is_instance_valid(_input_component):
		push_error("PlayerService: Failed to find InputComponent.")

	_health_component = _player_node.find_child(&"HealthComponent", true, false)
	if not is_instance_valid(_health_component):
		push_error("PlayerService: Failed to find HealthComponent.")

	_stats_component = _player_node.find_child(&"StatsComponent", true, false)
	if not is_instance_valid(_stats_component):
		push_error("PlayerService: Failed to find StatsComponent.")

	_hitbox = _player_node.find_child(&"Hitbox", true, false)
	if not is_instance_valid(_hitbox):
		push_error("PlayerService: Failed to find Hitbox.")

	player_initialized.emit(_player_node)

func get_player_node() -> Node2D:
	return _player_node

func get_movement_component() -> MovementComponent:
	return _movement_component

func get_paint_component() -> PaintComponent:
	return _paint_component

func get_ability_component() -> AbilityComponent:
	return _ability_component

func get_input_component() -> InputComponent:
	return _input_component

func get_health_component() -> HealthComponent:
	return _health_component

func get_stats_component() -> StatsComponent:
	return _stats_component

func get_hitbox() -> Area2D:
	return _hitbox
</file>

<file path="player/player_service.gd.uid">
uid://cndyjg0c5r3k4
</file>

<file path="player/player.gd">
extends CharacterBody2D

@export var paint_component: PaintComponent

func _ready() -> void:
	paint_component.reset()

func die() -> void:
	queue_free()
</file>

<file path="player/player.gd.uid">
uid://bwkg6ydcldanv
</file>

<file path="player/player.tscn">
[gd_scene load_steps=19 format=3 uid="uid://dq0bjiqkyl4wv"]

[ext_resource type="Texture2D" uid="uid://cj35vjs6j4qr3" path="res://assets/banana.png" id="1_0fpyy"]
[ext_resource type="Script" uid="uid://bwkg6ydcldanv" path="res://src/player/player.gd" id="1_a7o36"]
[ext_resource type="PackedScene" uid="uid://b2minqce3p1bg" path="res://src/health/health_component.tscn" id="4_xi0yy"]
[ext_resource type="PackedScene" path="res://src/paint/paint_component.tscn" id="5_paint_component"]
[ext_resource type="PackedScene" uid="uid://d0ntxy5trg0re" path="res://src/ability/ability_component.tscn" id="6_ability_component"]
[ext_resource type="Script" uid="uid://72hm77eoq5jd" path="res://src/ability/ability_data.gd" id="7_vqmnj"]
[ext_resource type="Script" uid="uid://dcms2verply6s" path="res://src/ability/restore_paint_ability_data.gd" id="8_vqmnj"]
[ext_resource type="Script" uid="uid://5g3i8wjai3b2" path="res://src/ability/maintain_normal_time_ability_data.gd" id="9_sh2yo"]
[ext_resource type="Script" uid="uid://b5xc2p1kg3ho6" path="res://src/ability/wall_phase_ability_data.gd" id="10_aco0u"]
[ext_resource type="Script" uid="uid://bkusl4o508on1" path="res://src/ability/multi_drop_ability_data.gd" id="11_multi_drop"]
[ext_resource type="PackedScene" uid="uid://k2v1icx8llrc" path="res://src/movement/movement_component.tscn" id="12_aco0u"]
[ext_resource type="PackedScene" uid="uid://bncyxtrrca85p" path="res://src/player/input_component.tscn" id="14_77kuh"]
[ext_resource type="PackedScene" uid="uid://y6tbdx4l0wxy" path="res://src/player/stats_component.tscn" id="16_stats_component"]

[sub_resource type="Resource" id="Resource_sh2yo"]
script = ExtResource("8_vqmnj")
paint_to_restore = 1
metadata/_custom_type_script = "uid://dcms2verply6s"

[sub_resource type="Resource" id="Resource_aco0u"]
script = ExtResource("9_sh2yo")
metadata/_custom_type_script = "uid://5g3i8wjai3b2"

[sub_resource type="Resource" id="Resource_cfk8s"]
script = ExtResource("10_aco0u")
paint_cost = 1
metadata/_custom_type_script = "uid://b5xc2p1kg3ho6"

[sub_resource type="Resource" id="Resource_multi_drop"]
script = ExtResource("11_multi_drop")
drop_count = 3

[sub_resource type="CircleShape2D" id="CircleShape2D_0fpyy"]
radius = 3.0

[node name="Player" type="CharacterBody2D" node_paths=PackedStringArray("paint_component") groups=["player"]]
motion_mode = 1
script = ExtResource("1_a7o36")
paint_component = NodePath("PaintComponent")

[node name="StatsComponent" parent="." instance=ExtResource("16_stats_component")]

[node name="HealthComponent" parent="." instance=ExtResource("4_xi0yy")]

[node name="PaintComponent" parent="." instance=ExtResource("5_paint_component")]

[node name="AbilityComponent" parent="." instance=ExtResource("6_ability_component")]
ability_data_list = Array[ExtResource("7_vqmnj")]([SubResource("Resource_sh2yo"), SubResource("Resource_aco0u"), SubResource("Resource_cfk8s"), SubResource("Resource_multi_drop")])

[node name="MovementComponent" parent="." node_paths=PackedStringArray("actor", "collision_ray") instance=ExtResource("12_aco0u")]
actor = NodePath("..")
collision_ray = NodePath("../CollisionRay")

[node name="InputComponent" parent="." instance=ExtResource("14_77kuh")]

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
position = Vector2(0, -4)
texture = ExtResource("1_0fpyy")

[node name="Collision" type="CollisionShape2D" parent="."]
position = Vector2(1, -4)
shape = SubResource("CircleShape2D_0fpyy")
debug_color = Color(0.282804, 0.501787, 0.99058, 0.666667)

[node name="Hitbox" type="Area2D" parent="."]
position = Vector2(0, -4)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Hitbox"]
position = Vector2(1, 1)
shape = SubResource("CircleShape2D_0fpyy")
debug_color = Color(0.329214, 0.61418, 0.295959, 0.5)

[node name="CollisionRay" type="RayCast2D" parent="."]
target_position = Vector2(0, 0)
</file>

<file path="player/stats_component.gd">
class_name StatsComponent
extends Node

@export var base_stats: PlayerBaseStatsData
</file>

<file path="player/stats_component.gd.uid">
uid://bdhqpv3u52d27
</file>

<file path="player/stats_component.tscn">
[gd_scene load_steps=2 format=3 uid="uid://y6tbdx4l0wxy"]

[ext_resource type="Script" path="res://src/player/stats_component.gd" id="1_stats_component"]

[node name="StatsComponent" type="Node"]
script = ExtResource("1_stats_component")
</file>

<file path="progress_bar/event_marker.gd">
class_name EventMarker
extends Node2D

@export var icon_sprite: Sprite2D

func set_icon(texture: Texture2D) -> void:
	icon_sprite.texture = texture
</file>

<file path="progress_bar/event_marker.gd.uid">
uid://cqynm5a5ixadw
</file>

<file path="progress_bar/event_marker.tscn">
[gd_scene load_steps=2 format=3 uid="uid://dhf7god1f8kxn"]

[ext_resource type="Script" uid="uid://cqynm5a5ixadw" path="res://src/progress_bar/event_marker.gd" id="2_event_marker"]

[node name="EventMarker" type="Node2D" node_paths=PackedStringArray("icon_sprite")]
script = ExtResource("2_event_marker")
icon_sprite = NodePath("Icon")

[node name="Icon" type="Sprite2D" parent="."]
texture_filter = 1
position = Vector2(0, -9)

[node name="Line" type="ColorRect" parent="."]
offset_top = -4.0
offset_right = 1.0
offset_bottom = -1.0
color = Color(0.0980392, 0.129412, 0.254902, 1)
</file>

<file path="progress_bar/progress_bar.gd">
extends Node2D

@export var progress_bar: ProgressBar
@export var margin_container: MarginContainer
@export var level_state_component: LevelStateComponent
@export var event_marker_scene: PackedScene
@export var markers_container: Node2D

var markers: Array[Node2D] = []

func _ready() -> void:
	level_state_component.data.progress_changed.connect(_on_progress_changed)
	level_state_component.data.event_triggered.connect(_on_event_triggered)
	_on_progress_changed(level_state_component.data.get_progress())
	_populate_event_markers.call_deferred()

func _on_progress_changed(new_progress: float) -> void:
	progress_bar.value = new_progress

func _on_event_triggered(event_index: int) -> void:
	if event_index < 0 or event_index >= markers.size():
		return

	var marker_to_remove: Node2D = markers[event_index]
	if is_instance_valid(marker_to_remove):
		markers[event_index] = null
		marker_to_remove.queue_free()

func _populate_event_markers() -> void:
	for child in markers_container.get_children():
		child.queue_free()
	markers.clear()

	var bar_width: float = progress_bar.size.x
	var data: LevelStateData = level_state_component.data

	for i in range(data.events.size()):
		var event_data: LevelEventData = data.events[i]
		var marker: EventMarker = event_marker_scene.instantiate() as EventMarker
		marker.name = "EventMarker_" + str(i)

		print(event_data.icon)

		marker.set_icon(event_data.icon)

		markers_container.add_child(marker)
		markers.append(marker)

		var left_margin: float = margin_container.get_theme_constant("margin_left")
		var right_margin: float = margin_container.get_theme_constant("margin_right")
		var usable_width: float = bar_width - left_margin - right_margin

		var position_x: float = left_margin + (event_data.progress_threshold * usable_width)
		marker.position = Vector2(position_x, 0)
</file>

<file path="progress_bar/progress_bar.gd.uid">
uid://6bqpdastrjla
</file>

<file path="progress_bar/progress_bar.tscn">
[gd_scene load_steps=5 format=3 uid="uid://bv3kc8uwrke6f"]

[ext_resource type="Script" uid="uid://6bqpdastrjla" path="res://src/progress_bar/progress_bar.gd" id="1_jl4r8"]
[ext_resource type="Texture2D" uid="uid://croipc1prk67w" path="res://assets/paint_bar_texture.png" id="1_x8s5q"]
[ext_resource type="PackedScene" uid="uid://dhf7god1f8kxn" path="res://src/progress_bar/event_marker.tscn" id="2_jl4r8"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_x8s5q"]
bg_color = Color(1, 0.913725, 0.133333, 1)

[node name="ProgressBar" type="Node2D" node_paths=PackedStringArray("progress_bar", "margin_container", "markers_container")]
script = ExtResource("1_jl4r8")
progress_bar = NodePath("NinePatchRect/MarginContainer/ProgressBar")
margin_container = NodePath("NinePatchRect/MarginContainer")
event_marker_scene = ExtResource("2_jl4r8")
markers_container = NodePath("MarkersContainer")

[node name="NinePatchRect" type="NinePatchRect" parent="."]
texture_filter = 1
offset_right = 120.0
offset_bottom = 8.0
texture = ExtResource("1_x8s5q")
region_rect = Rect2(0, 0, 8, 8)
patch_margin_left = 1
patch_margin_top = 1
patch_margin_right = 1
patch_margin_bottom = 1

[node name="MarginContainer" type="MarginContainer" parent="NinePatchRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 2
theme_override_constants/margin_top = 2
theme_override_constants/margin_right = 2
theme_override_constants/margin_bottom = 2

[node name="ProgressBar" type="ProgressBar" parent="NinePatchRect/MarginContainer"]
layout_mode = 2
theme_override_styles/fill = SubResource("StyleBoxFlat_x8s5q")
max_value = 1.0
step = 0.001
value = 0.53
show_percentage = false

[node name="MarkersContainer" type="Node2D" parent="."]
</file>

<file path="run_state/run_state_service.gd">
class_name RunStateService
extends Node

var total_painted_tiles_in_run: int = 0

func reset() -> void:
	total_painted_tiles_in_run = 0

func add_painted_tiles(count: int) -> void:
	if count > 0:
		total_painted_tiles_in_run += count

func get_total_painted_tiles() -> int:
	return total_painted_tiles_in_run
</file>

<file path="run_state/run_state_service.gd.uid">
uid://dmd7tkr213qy7
</file>

<file path="stats/stat_service.gd">
extends Node

const HEALTH_PER_UPGRADE = 1
const PAINT_PER_UPGRADE = 2
const MOVE_DURATION_REDUCTION_PER_UPGRADE = 0.01
const EXTRA_LIVES_PER_UPGRADE = 1
const SIZE_MODIFIER_PER_UPGRADE = 0.05

var _effective_max_health: int
var _effective_max_paint: int
var _effective_move_duration: float
var _effective_extra_lives: int
var _effective_size_modifier: float
var _paint_color: Color
var _tile_size: int
var _initial_repeat_delay: float
var _repeat_rate: float

func initialize_for_run(player_stats_component: StatsComponent) -> void:
	var base := player_stats_component.base_stats

	_effective_max_health = base.max_health + (GameProgress.get_stat_upgrade_level("max_health") * HEALTH_PER_UPGRADE)
	_effective_max_paint = base.max_paint + (GameProgress.get_stat_upgrade_level("max_paint") * PAINT_PER_UPGRADE)
	_effective_move_duration = base.move_duration - (GameProgress.get_stat_upgrade_level("move_duration") * MOVE_DURATION_REDUCTION_PER_UPGRADE)
	_effective_extra_lives = base.extra_lives + (GameProgress.get_stat_upgrade_level("extra_lives") * EXTRA_LIVES_PER_UPGRADE)
	_effective_size_modifier = base.size_modifier + (GameProgress.get_stat_upgrade_level("size_modifier") * SIZE_MODIFIER_PER_UPGRADE)

	_paint_color = base.paint_color
	_tile_size = base.tile_size
	_initial_repeat_delay = base.initial_repeat_delay
	_repeat_rate = base.repeat_rate

func spend_extra_life() -> bool:
	if _effective_extra_lives > 0:
		_effective_extra_lives -= 1
		return true
	return false

func get_max_health() -> int:
	return _effective_max_health

func get_max_paint() -> int:
	return _effective_max_paint

func get_move_duration() -> float:
	return max(0.05, _effective_move_duration)

func get_extra_lives() -> int:
	return _effective_extra_lives

func get_size_modifier() -> float:
	return _effective_size_modifier

func get_paint_color() -> Color:
	return _paint_color

func get_tile_size() -> int:
	return _tile_size

func get_initial_repeat_delay() -> float:
	return _initial_repeat_delay

func get_repeat_rate() -> float:
	return _repeat_rate
</file>

<file path="stats/stat_service.gd.uid">
uid://bjuemmcabvyi8
</file>

<file path="tile_query_system/tile_query_system.gd">
class_name TileQuerySystem
extends Node

var _tile_map: Dictionary = {}

func get_tile_at_global_pos(position: Vector2) -> Node2D:
	return _tile_map.get(position, null)

func get_nearest_tile(position: Vector2) -> Node2D:
	if _tile_map.is_empty():
		return null

	var nearest_tile: Node2D = null
	var min_dist_sq: float = INF

	for tile_pos: Vector2 in _tile_map.keys():
		var dist_sq: float = position.distance_squared_to(tile_pos)
		if dist_sq < min_dist_sq:
			min_dist_sq = dist_sq
			nearest_tile = _tile_map[tile_pos]

	return nearest_tile

func get_random_tile() -> Node2D:
	if _tile_map.is_empty():
		return null

	var keys := _tile_map.keys()
	var random_index := randi() % keys.size()
	return _tile_map[keys[random_index]]

func build_map() -> void:
	_tile_map.clear()
	var tiles: Array[Node] = get_tree().get_nodes_in_group(&"tiles")
	for tile_node: Node2D in tiles:
		_tile_map[tile_node.global_position] = tile_node
</file>

<file path="tile_query_system/tile_query_system.gd.uid">
uid://hoa0cd336u8s
</file>

<file path="tile_query_system/tile_query_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://biw4o1x1u6l4i"]

[ext_resource type="Script" uid="uid://hoa0cd336u8s" path="res://src/tile_query_system/tile_query_system.gd" id="1_system"]

[node name="TileQuerySystem" type="Node"]
script = ExtResource("1_system")
</file>

<file path="time_dilation/time_dilation_system.gd">
class_name TimeDilationSystem
extends Node

@export var tile_query_system: TileQuerySystem
@export var player_service: PlayerService

var _player_movement_component: MovementComponent
var _player_ability_component: AbilityComponent

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	_player_movement_component = player_service.get_movement_component()
	_player_ability_component = player_service.get_ability_component()

	_player_movement_component.movement_started.connect(_on_player_movement_started)
	_player_movement_component.movement_completed.connect(_on_player_movement_completed)

	_set_time_state(false)

func _on_player_movement_started(_direction: Vector2) -> void:
	var actor: Node2D = _player_movement_component.actor
	var parent_node: Node2D = actor.get_parent() as Node2D
	var global_target_pos: Vector2 = parent_node.to_global(_player_movement_component.target_position)

	var target_tile: Node2D = tile_query_system.get_tile_at_global_pos(global_target_pos)
	var should_speed_up: bool = true
	if is_instance_valid(target_tile) and target_tile is ColorTile and (target_tile as ColorTile).is_painted() and _player_has_ability(MaintainNormalTimeAbilityData):
		should_speed_up = false

	_set_time_state(should_speed_up)

func _on_player_movement_completed() -> void:
	_set_time_state(false)

func _player_has_ability(ability_type: GDScript) -> bool:
	if not is_instance_valid(_player_ability_component):
		return false
	return _player_ability_component.has_ability(ability_type)

func _set_time_state(is_player_moving: bool) -> void:
	var components: Array[Node] = get_tree().get_nodes_in_group(&"time_sensitive")
	for node in components:
		var component: TimeSensitiveComponent = node as TimeSensitiveComponent
		if not is_instance_valid(component):
			continue
		if is_player_moving:
			component.current_speed = component.data.normal_speed
			component.current_rotation_speed = component.data.normal_rotation_speed
		else:
			component.current_speed = component.data.slow_speed
			component.current_rotation_speed = component.data.slow_rotation_speed
</file>

<file path="time_dilation/time_dilation_system.gd.uid">
uid://bq8hitu1uuymk
</file>

<file path="time_dilation/time_dilation_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://e0dt5jwwxndr"]

[ext_resource type="Script" path="res://src/time_dilation/time_dilation_system.gd" id="1_system"]

[node name="TimeDilationSystem" type="Node"]
script = ExtResource("1_system")
</file>

<file path="time_dilation/time_sensitive_component.gd">
class_name TimeSensitiveComponent
extends Node

@export var data: TimeSensitiveData

var current_speed: float = 0.0
var current_rotation_speed: float = 0.0

func _ready() -> void:
	add_to_group(&"time_sensitive")
	current_speed = data.slow_speed
	current_rotation_speed = data.slow_rotation_speed
</file>

<file path="time_dilation/time_sensitive_component.gd.uid">
uid://bitxglaai28lf
</file>

<file path="time_dilation/time_sensitive_component.tscn">
[gd_scene load_steps=2 format=3 uid="uid://db6e8chc7nyk3"]

[ext_resource type="Script" uid="uid://bitxglaai28lf" path="res://src/time_dilation/time_sensitive_component.gd" id="1_component"]

[node name="TimeSensitiveComponent" type="Node" groups=["time_sensitive"]]
script = ExtResource("1_component")
</file>

<file path="time_dilation/time_sensitive_data.gd">
class_name TimeSensitiveData
extends Resource

@export var slow_speed: float = 50.0
@export var normal_speed: float = 300.0
@export var slow_rotation_speed: float = 1.0
@export var normal_rotation_speed: float = 10.0
</file>

<file path="time_dilation/time_sensitive_data.gd.uid">
uid://clrphpo8v8ep1
</file>

<file path="water/water_system.gd">
class_name WaterSystem
extends Node

@export var tile_query_system: TileQuerySystem
@export var player_service: PlayerService

var _player: CharacterBody2D
var _paint_component: PaintComponent
var _movement_component: MovementComponent
var _hitbox: Area2D

var _is_underwater: bool = false
var _saved_collision_layer: int = 0
var _saved_monitorable: bool = true

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	_player = player_service.get_player_node()
	_paint_component = player_service.get_paint_component()
	_movement_component = player_service.get_movement_component()
	_hitbox = player_service.get_hitbox()

	_movement_component.movement_completed.connect(_on_move_completed)
	_update_state()

func _on_move_completed() -> void:
	_update_state()
	_apply_paint_cost()

func _apply_paint_cost() -> void:
	if not _is_underwater:
		return
	var tile_component: WaterTileComponent = _current_water_tile()
	if not is_instance_valid(tile_component):
		return
	if _paint_component.current_paint > 0:
		_paint_component.current_paint -= tile_component.data.paint_cost

func _update_state() -> void:
	var was_underwater: bool = _is_underwater
	_is_underwater = is_instance_valid(_current_water_tile())
	if was_underwater == _is_underwater:
		return
	if _is_underwater:
		_saved_collision_layer = _player.collision_layer
		_player.collision_layer = 0
		_saved_monitorable = _hitbox.monitorable
		_hitbox.monitorable = false
	else:
		_player.collision_layer = _saved_collision_layer
		_hitbox.monitorable = _saved_monitorable

func _current_water_tile() -> WaterTileComponent:
	var tile: Node2D = tile_query_system.get_tile_at_global_pos(_player.global_position)
	if not is_instance_valid(tile) or tile is not WaterTileComponent:
		return null
	return tile
</file>

<file path="water/water_system.gd.uid">
uid://b4jv8dmd4b5ae
</file>

<file path="water/water_system.tscn">
[gd_scene load_steps=2 format=3 uid="uid://bnbdlymwq0s8q"]

[ext_resource type="Script" uid="uid://b4jv8dmd4b5ae" path="res://src/water/water_system.gd" id="1_hdcu1"]

[node name="WaterSystem" type="Node"]
script = ExtResource("1_hdcu1")
</file>

<file path="water_tile/water_tile_component.gd">
class_name WaterTileComponent
extends Node2D

@export var data: WaterTileData

func _ready() -> void:
	add_to_group("water_tiles")
	add_to_group("tiles")
</file>

<file path="water_tile/water_tile_component.gd.uid">
uid://7ypb2u0vebs0
</file>

<file path="water_tile/water_tile_data.gd">
class_name WaterTileData
extends Resource

@export var paint_cost: int = 1
</file>

<file path="water_tile/water_tile_data.gd.uid">
uid://8gsgmtcloxcl
</file>

<file path="water_tile/water_tile.gd.uid">
uid://dlvdgodobna17
</file>

<file path="water_tile/water_tile.tscn">
[gd_scene load_steps=7 format=3 uid="uid://dfy35tmjoeoht"]

[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="1_7ub1g"]
[ext_resource type="Script" uid="uid://7ypb2u0vebs0" path="res://src/water_tile/water_tile_component.gd" id="1_ds36l"]
[ext_resource type="Script" uid="uid://8gsgmtcloxcl" path="res://src/water_tile/water_tile_data.gd" id="2_5hfh0"]

[sub_resource type="Resource" id="Resource_125nn"]
script = ExtResource("2_5hfh0")
paint_cost = 1
metadata/_custom_type_script = "uid://8gsgmtcloxcl"

[sub_resource type="AtlasTexture" id="AtlasTexture_ds36l"]
atlas = ExtResource("1_7ub1g")
region = Rect2(0, 0, 8, 8)

[sub_resource type="CircleShape2D" id="CircleShape2D_5hfh0"]
radius = 3.0

[node name="WaterTile" type="Node2D"]
script = ExtResource("1_ds36l")
data = SubResource("Resource_125nn")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = SubResource("AtlasTexture_ds36l")

[node name="ColorRect" type="ColorRect" parent="."]
offset_left = -4.0
offset_top = -4.0
offset_right = 3.0
offset_bottom = 3.0
color = Color(0.223529, 0.686275, 0.941176, 1)

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("CircleShape2D_5hfh0")
</file>

<file path="win_ui/win_ui.gd">
extends CanvasLayer

signal play_again_pressed

@export var play_again_button: Button

var level_node: Node2D

func _ready() -> void:
	play_again_button.pressed.connect(_on_play_again_pressed)
	visibility_changed.connect(_on_visibility_changed)
	level_node = get_parent() as Node2D
	hide()

func _on_visibility_changed() -> void:
	show()

	if not is_instance_valid(level_node):
		return
	if not level_node.has_method("get"):
		return
	var level_id: Variant = level_node.get("level_id")
	if not level_id is String:
		return
	var level_id_str: String = level_id
	if level_id_str.is_empty():
		return

	GameProgress.mark_level_as_completed(level_id_str)

func _on_play_again_pressed() -> void:
	play_again_pressed.emit()
</file>

<file path="win_ui/win_ui.gd.uid">
uid://c3bxkbvpniqlr
</file>

<file path="win_ui/win_ui.tscn">
[gd_scene load_steps=2 format=3 uid="uid://cb5k3begpk4sc"]

[ext_resource type="Script" uid="uid://c3bxkbvpniqlr" path="res://src/win_ui/win_ui.gd" id="1_0"]

[node name="WinUI" type="CanvasLayer" node_paths=PackedStringArray("play_again_button")]
script = ExtResource("1_0")
play_again_button = NodePath("Control/VBoxContainer/PlayAgainButton")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="ColorRect" type="ColorRect" parent="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.5)

[node name="VBoxContainer" type="VBoxContainer" parent="Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -50.0
offset_right = 100.0
offset_bottom = 50.0
grow_horizontal = 2
grow_vertical = 2

[node name="WinLabel" type="Label" parent="Control/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 100
text = "Победа!"
horizontal_alignment = 1

[node name="PlayAgainButton" type="Button" parent="Control/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 70
text = "Играть снова"
</file>

<file path="README.md">
**Название игры:** (Рабочее название: Fruit Rogue)

**Жанр:** Аркадный рогалик с механиками контроля территории и менеджментом ресурсов.

**Ключевые геймплейные механики:**

1.  **Система Ресурсов:**
    *   **Краска (Топливо):** Основной расходуемый ресурс, находящийся в "баке" игрока. Тратится по 1 единице за каждый закрашенный тайл. Также может расходоваться на способности от некоторых артефактов. Восполняется подбором капель краски на уровне.
    *   **Закрашенные Тайлы (Валюта):** Накопительный счетчик всех уникальных тайлов, закрашенных игроком за текущий забег. Это единственная валюта, которая тратится в магазинах на покупку артефактов.

2.  **Покраска и Цель:**
    *   При перемещении на незакрашенную клетку, игрок **тратит 1 единицу Краски (Топлива)**, чтобы ее закрасить. После этого счетчик валюты "Закрашенные Тайлы" увеличивается на 1. Если у игрока нет Краски (Топлива), он не может закрашивать новые тайлы.
    *   Цель в каждой "боевой" комнате — закрасить 100% доступных для покраски тайлов, чтобы открыть выход.

3.  **Взаимодействие с врагами (Пилами):**
    *   Базовый геймплей построен на уклонении от движущихся пил. Игрок начинает забег без каких-либо атакующих способностей.
    *   Существуют разные типы пил с разным поведением.

4.  **Свойства комнат:**
    *   **Комнаты-бублики:** Особый тип комнат, где все сущности (игрок и враги) могут перемещаться за границу экрана и появляться с противоположной стороны.

**Система артефактов:**

Игрок начинает каждый забег без артефактов. Все способности, баффы и пассивные эффекты приобретаются во время забега в виде артефактов.

**1. Артефакты, изменяющие правила боя и движения (предложенные в обсуждении):**
*   **"Прыжок-убийство":** Позволяет перемещаться через одну клетку, уничтожая стандартную пилу на ней.
*   **"Разрушающий удар":** Позволяет разрушать специальные блоки. Во время разрушения игрок уязвим, так как мир вокруг него временно ускоряется.
*   **"Призрачный шаг":** Позволяет игроку проходить сквозь границы экрана в любой комнате.

**2. Артефакты, изменяющие правила покраски и экономики (предложенные в обсуждении):**
*   **"Замыкание контура":** Дает бонусную валюту за закрашивание областей путем рисования замкнутой линии.
*   **"Соединение островов":** Дает бонусную валюту за соединение двух закрашенных участков в один.

**3. Артефакты, основанные на существующих механиках (из `AbilityData`):**
*   **"Фазовый сдвиг" (WallPhaseAbilityData):** Позволяет игроку проходить сквозь стены. При попытке движения в стену тратится Краска (Топливо), и игрок телепортируется до первой свободной клетки в этом направлении.
*   **"Переработка чернил" (RestorePaintAbilityData):** Восстанавливает 1 единицу Краски (Топлива) каждый раз, когда игрок наступает на уже закрашенную клетку.
*   **"Рог изобилия" (MultiDropAbilityData):** Увеличивает количество капель краски, одновременно присутствующих на уровне.
*   **"Стабилизатор времени" (MaintainNormalTimeAbilityData):** Предотвращает ускорение времени, когда игрок движется по уже закрашенным клеткам. Враги остаются замедленными, позволяя безопасно маневрировать по своей территории.

**4. Система Проклятых Артефактов:**
*   Некоторые особо мощные артефакты при покупке дополнительно добавляют в инвентарь игрока отдельный, **Проклятый Артефакт** с негативным эффектом.
*   *Пример Проклятого Артефакта 1:* **"Недолговечные Чернила"**. Закрашенные тайлы начинают со временем исчезать (первым исчезает самый старый).
*   *Пример Проклятого Артефакта 2:* **"Инверсия" (InvertedControlsAbilityData)**. Инвертирует управление игрока. Может быть временным (как в коде) или постоянным проклятием на забег.

**Основной геймплейный цикл (Core Gameplay Loop):**

1.  **Вход в комнату:** Игрок появляется в комнате с врагами, каплями краски и незакрашенными тайлами.
2.  **Фаза менеджмента и покраски:** Игрок маневрирует между врагами, тратя **Краску (Топливо)** для покраски тайлов, что генерирует ему **Валюту**. Он должен следить за запасом Топлива и вовремя подбирать капли краски.
3.  **Зачистка комнаты:** Игрок закрашивает 100% тайлов, и выход открывается.
4.  **Переход:** Игрок переходит в следующую комнату (боевая, магазин, босс).
5.  **Магазин:** В комнатах-магазинах игрок тратит накопленную **Валюту ("Закрашенные Тайлы")** на покупку артефактов.
6.  **Повторение:** Цикл повторяется до встречи с боссом.

**Система прогрессии игрока:**

*   **Прогрессия внутри забега:** Построена исключительно на сборе и комбинировании артефактов. Игрок формирует уникальный билд способностей для каждого забега.
*   **Мета-прогрессия (между забегами):**
    *   После победы над боссом появляется "банкомат". Игрок может конвертировать **все свои оставшиеся, не потраченные в магазинах "Закрашенные Тайлы"** в мета-валюту.
    *   В хабе (вне забега) мета-валюта тратится на постоянное улучшение базовых статов персонажа: `scale`, `sugar`, `juice`, `fiber`, `shell`.

**Уникальные особенности геймплея (USP):**

*   **Экономика Конвертации "Топливо в Валюту":** Игрок активно тратит один конечный ресурс (Краску-топливо) для генерации другого (Закрашенные тайлы-валюту), что создает постоянный цикл риска, менеджмента ресурсов и поиска восполнения.
*   **Чистый билд-ориентированный геймплей:** Прогрессия в забеге зависит исключительно от выбора артефактов и их синергии, а не от числовых улучшений.
*   **Напряжение экономики "Сейчас или Потом":** Потратив валюту на артефакты для усиления в текущем забеге, игрок уменьшает количество валюты, которую сможет конвертировать в постоянную мета-прогрессию в конце.
*   **Модульная система риска:** Проклятия реализованы как отдельные артефакты, что позволяет гибко балансировать игру, привязывая стандартизированные негативные эффекты к разным мощным бонусам.
</file>

</files>
